-- 为经济学编年史表添加卡片位置字段
-- 创建时间: 2025-01-08

-- 添加card_position字段到ba_economics_history表
ALTER TABLE `ba_economics_history` 
ADD COLUMN `card_position` ENUM('above', 'below') DEFAULT 'above' COMMENT '卡片显示位置：above=时间轴上方，below=时间轴下方' 
AFTER `importance_level`;

-- 添加索引以提高查询性能
ALTER TABLE `ba_economics_history` 
ADD INDEX `idx_card_position` (`card_position`);

-- 更新现有数据，设置默认值为'above'
UPDATE `ba_economics_history` 
SET `card_position` = 'above' 
WHERE `card_position` IS NULL;

-- 验证字段添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_DEFAULT,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'ba_economics_history' 
  AND COLUMN_NAME = 'card_position';

-- 显示表结构确认
DESCRIBE `ba_economics_history`;
