/**
 * 本地存储工具类
 * 用于管理用户偏好设置和应用配置
 */

// 存储前缀，避免与其他应用冲突
const STORAGE_PREFIX = 'operation_delta_web_'

/**
 * 本地存储工具类
 */
export class Storage {
  /**
   * 设置本地存储项
   * @param key 键名
   * @param value 值
   * @param usePrefix 是否使用前缀，默认true
   */
  static set(key: string, value: any, usePrefix: boolean = true): void {
    const storageKey = usePrefix ? `${STORAGE_PREFIX}${key}` : key

    try {
      const storageValue = JSON.stringify({
        value,
        timestamp: new Date().getTime()
      })

      localStorage.setItem(storageKey, storageValue)
    } catch (error) {
      console.error('存储数据失败:', error)
    }
  }
  
  /**
   * 获取本地存储项
   * @param key 键名
   * @param defaultValue 默认值
   * @param usePrefix 是否使用前缀，默认true
   */
  static get<T>(key: string, defaultValue: T | null = null, usePrefix: boolean = true): T | null {
    const storageKey = usePrefix ? `${STORAGE_PREFIX}${key}` : key

    try {
      const storageItem = localStorage.getItem(storageKey)

      if (!storageItem) {
        return defaultValue
      }

      const { value } = JSON.parse(storageItem)
      return value as T
    } catch (error) {
      console.error('获取存储数据失败:', error)
      return defaultValue
    }
  }
  
  /**
   * 移除本地存储项
   * @param key 键名
   * @param usePrefix 是否使用前缀，默认true
   */
  static remove(key: string, usePrefix: boolean = true): void {
    const storageKey = usePrefix ? `${STORAGE_PREFIX}${key}` : key
    localStorage.removeItem(storageKey)
  }
  
  /**
   * 清除所有带前缀的存储项
   */
  static clear(): void {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(STORAGE_PREFIX)) {
        localStorage.removeItem(key)
      }
    })
  }

  /**
   * 清除经济学编年史相关的缓存
   */
  static clearEconomicsHistoryCache(): void {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      // 清除经济学编年史相关的缓存
      if (key.includes('economics_history') ||
          key.includes('event_type') ||
          key.includes('season_history')) {
        localStorage.removeItem(key)
        console.log('已清除缓存:', key)
      }
    })

    // 清除内存缓存（如果有的话）
    if (typeof window !== 'undefined' && (window as any).economicsHistoryCache) {
      (window as any).economicsHistoryCache = {}
    }
  }

  /**
   * 保存编辑模式的账号密码
   */
  static saveEditCredentials(password: string): void {
    this.set('edit_password', password)
  }

  /**
   * 获取编辑模式的账号密码
   */
  static getEditCredentials(): string {
    return this.get('edit_password', '')
  }

  /**
   * 清除编辑模式的账号密码
   */
  static clearEditCredentials(): void {
    this.remove('edit_password')
  }
}

/**
 * 赛季任务进度数据接口
 */
export interface SeasonProgressData {
  season: string
  lastUpdate: string
  version: string
  tasks: Record<number, TaskProgress>
}

/**
 * 单个任务进度接口
 */
export interface TaskProgress {
  status: 0 | 1 | 2  // 0-未开始，1-进行中，2-已完成
  progress: number   // 进度百分比 0-100
  completedAt?: string
  updatedAt?: string
  progressData?: Record<string, any>  // 自定义进度数据
}

/**
 * 用户偏好设置管理
 */
export class UserPreferences {
  /**
   * 图表时间段偏好设置键名
   */
  static readonly CHART_PERIOD_KEY = 'chart_period_preference'

  /**
   * 图表时间点过滤偏好设置键名
   */
  static readonly CHART_TIME_FILTER_KEY = 'chart_time_filter_preference'

  /**
   * 赛季任务进度相关键名
   */
  static readonly SEASON_PROGRESS_KEY = 'season_progress'

  /**
   * 获取图表时间段偏好
   * @param defaultPeriod 默认时间段，默认为'7d'
   * @returns 用户偏好的时间段
   */
  static getChartPeriod(defaultPeriod: string = '7d'): string {
    return Storage.get<string>(this.CHART_PERIOD_KEY, defaultPeriod) || defaultPeriod
  }

  /**
   * 设置图表时间段偏好
   * @param period 时间段值
   */
  static setChartPeriod(period: string): void {
    Storage.set(this.CHART_PERIOD_KEY, period)
  }

  /**
   * 获取图表时间点过滤偏好
   * @param defaultHours 默认时间点数组，默认为空数组（显示所有时间点）
   * @returns 用户偏好的时间点数组
   */
  static getChartTimeFilter(defaultHours: number[] = []): number[] {
    return Storage.get<number[]>(this.CHART_TIME_FILTER_KEY, defaultHours) || defaultHours
  }

  /**
   * 设置图表时间点过滤偏好
   * @param hours 时间点数组，例如 [1, 5, 10, 20] 表示只显示1点、5点、10点、20点的数据
   */
  static setChartTimeFilter(hours: number[]): void {
    Storage.set(this.CHART_TIME_FILTER_KEY, hours)
  }

  /**
   * 调试方法：检查localStorage中的实际存储内容
   */
  static debugStorage(): void {
    const fullKey = `operation_delta_web_${this.CHART_PERIOD_KEY}`
    const rawValue = localStorage.getItem(fullKey)
    console.log('=== 存储调试信息 ===')
    console.log('完整键名:', fullKey)
    console.log('原始存储值:', rawValue)
    if (rawValue) {
      try {
        const parsed = JSON.parse(rawValue)
        console.log('解析后的值:', parsed)
        console.log('实际时间段值:', parsed.value)
      } catch (e) {
        console.error('解析存储值失败:', e)
      }
    }
    console.log('通过getChartPeriod获取:', this.getChartPeriod())
    console.log('==================')
  }

  /**
   * 测试方法：手动测试存储功能
   */
  static testStorage(): void {
    console.log('=== 开始存储功能测试 ===')

    // 测试存储
    const testValue = '30d'
    console.log('1. 测试存储值:', testValue)
    this.setChartPeriod(testValue)

    // 测试读取
    console.log('2. 测试读取:')
    const retrieved = this.getChartPeriod()
    console.log('读取到的值:', retrieved)

    // 验证结果
    if (retrieved === testValue) {
      console.log('✅ 存储功能正常')
    } else {
      console.log('❌ 存储功能异常')
    }

    console.log('========================')
  }

  /**
   * 获取赛季任务进度
   * @param season 赛季名称，默认'S5'
   * @returns 赛季进度数据
   */
  static getSeasonProgress(season: string = 'S5'): SeasonProgressData {
    const defaultData: SeasonProgressData = {
      season,
      lastUpdate: new Date().toISOString(),
      version: '1.0',
      tasks: {}
    }
    
    const progressKey = `${this.SEASON_PROGRESS_KEY}_${season}`
    return Storage.get<SeasonProgressData>(progressKey, defaultData) || defaultData
  }

  /**
   * 保存赛季任务进度
   * @param season 赛季名称
   * @param progressData 进度数据
   */
  static setSeasonProgress(season: string, progressData: SeasonProgressData): void {
    const progressKey = `${this.SEASON_PROGRESS_KEY}_${season}`
    progressData.lastUpdate = new Date().toISOString()
    Storage.set(progressKey, progressData)
  }

  /**
   * 更新单个任务进度
   * @param season 赛季名称
   * @param taskKey 任务key
   * @param progress 进度数据
   */
  static updateTaskProgress(season: string, taskKey: number, progress: Partial<TaskProgress>): void {
    const progressData = this.getSeasonProgress(season)
    progressData.tasks[taskKey] = {
      status: 0,
      progress: 0,
      ...progressData.tasks[taskKey],
      ...progress,
      updatedAt: new Date().toISOString()
    }
    this.setSeasonProgress(season, progressData)
  }

  /**
   * 获取任务进度
   * @param season 赛季名称
   * @param taskKey 任务key
   * @returns 任务进度数据
   */
  static getTaskProgress(season: string, taskKey: number): TaskProgress | null {
    const progressData = this.getSeasonProgress(season)
    return progressData.tasks[taskKey] || null
  }

  /**
   * 标记任务完成
   * @param season 赛季名称
   * @param taskKey 任务key
   */
  static completeTask(season: string, taskKey: number): void {
    this.updateTaskProgress(season, taskKey, {
      status: 2,
      progress: 100,
      completedAt: new Date().toISOString()
    })
  }

  /**
   * 导出赛季进度为JSON文件
   * @param season 赛季名称
   */
  static exportSeasonProgress(season: string): void {
    const progressData = this.getSeasonProgress(season)
    const blob = new Blob([JSON.stringify(progressData, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `season_${season}_progress_${new Date().getTime()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 从文件导入赛季进度
   * @param file 文件对象
   * @returns Promise<boolean> 导入是否成功
   */
  static async importSeasonProgress(file: File): Promise<boolean> {
    try {
      const text = await file.text()
      const progressData: SeasonProgressData = JSON.parse(text)
      
      // 验证数据格式
      if (!progressData.season || !progressData.tasks) {
        throw new Error('无效的进度数据格式')
      }
      
      this.setSeasonProgress(progressData.season, progressData)
      return true
    } catch (error) {
      console.error('导入进度数据失败:', error)
      return false
    }
  }

  /**
   * 清除指定赛季的进度数据
   * @param season 赛季名称
   */
  static clearSeasonProgress(season: string): void {
    const progressKey = `${this.SEASON_PROGRESS_KEY}_${season}`
    Storage.remove(progressKey)
  }
}
