<?php
namespace app\common\model;

use think\Model;

/**
 * 经济学编年史模型
 */
class EconomicsHistory extends Model
{
    protected $name = 'economics_history';
    
    // 设置字段信息
    protected $schema = [
        'id'                => 'bigint',
        'season'            => 'varchar',
        'event_date'        => 'date',
        'event_time'        => 'time',
        'title'             => 'varchar',
        'description'       => 'text',
        'event_type_id'     => 'bigint',
        'event_type_custom' => 'varchar',
        'event_type_color'  => 'varchar',
        'tags'              => 'json',
        'importance_level'  => 'tinyint',
        'card_position'     => 'enum',
        'sort_order'        => 'int',
        'status'            => 'tinyint',
        'create_time'       => 'datetime',
        'update_time'       => 'datetime',
        'delete_time'       => 'datetime',
    ];

    // JSON 字段
    protected $json = ['tags'];
    
    // 字段类型转换
    protected $type = [
        'event_type_id'     => 'integer',
        'importance_level'  => 'integer',
        'sort_order'        => 'integer',
        'status'            => 'integer',
        'tags'              => 'array',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = 'delete_time';

    // 软删除
    use \think\model\concern\SoftDelete;

    /**
     * 关联事件类型
     */
    public function eventType()
    {
        return $this->belongsTo(EconomicsHistoryEventTypes::class, 'event_type_id', 'id');
    }

    /**
     * 获取赛季编年史
     * @param string $season 赛季
     * @return array
     */
    public function getSeasonHistory($season = 'S5')
    {
        trace('EconomicsHistory Model::getSeasonHistory 开始查询，season=' . $season, 'info');
        
        try {
            $query = $this->with('eventType')
                ->where('season', $season)
                ->where('status', 1)
                ->order('event_date desc, event_time desc, sort_order desc, id desc');
            
            trace('EconomicsHistory Model::getSeasonHistory SQL查询: ' . $query->fetchSql(true)->select(), 'info');
            
            $list = $query->select()->toArray();
            
            trace('EconomicsHistory Model::getSeasonHistory 原始查询结果: ' . json_encode($list, JSON_UNESCAPED_UNICODE), 'info');

            // 格式化数据：标签已经是JSON数组，无需额外处理
            foreach ($list as &$item) {
                // 确保标签是数组格式
                if (is_string($item['tags'])) {
                    $item['tags'] = json_decode($item['tags'], true) ?: [];
                } elseif (!is_array($item['tags'])) {
                    $item['tags'] = [];
                }
                
                // 格式化事件类型关联数据
                if (isset($item['eventType']) && $item['eventType']) {
                    $item['event_type'] = $item['eventType'];
                } else {
                    $item['event_type'] = null;
                }
                // 移除临时的 eventType 字段
                unset($item['eventType']);
            }
            
            trace('EconomicsHistory Model::getSeasonHistory 格式化后结果: ' . json_encode($list, JSON_UNESCAPED_UNICODE), 'info');

            return $list;
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::getSeasonHistory 查询异常: ' . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * 创建编年史事件
     * @param array $data
     * @return int|false
     */
    public function createEvent($data)
    {
        try {
            // 处理标签数据
            if (isset($data['tags'])) {
                if (is_string($data['tags'])) {
                    // 如果是JSON字符串，先解码再重新编码确保格式正确
                    $tags = json_decode($data['tags'], true) ?: [];
                    $data['tags'] = $tags;
                } elseif (!is_array($data['tags'])) {
                    $data['tags'] = [];
                }
            } else {
                $data['tags'] = [];
            }

            // 保存主记录（ThinkPHP会自动处理JSON字段）
            $this->save($data);
            return $this->id;
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::createEvent 创建失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 更新编年史事件
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateEvent($id, $data)
    {
        try {
            // 处理标签数据
            if (isset($data['tags'])) {
                if (is_string($data['tags'])) {
                    // 如果是JSON字符串，先解码再重新编码确保格式正确
                    $tags = json_decode($data['tags'], true) ?: [];
                    $data['tags'] = $tags;
                } elseif (!is_array($data['tags'])) {
                    $data['tags'] = [];
                }
            }

            // 更新主记录（ThinkPHP会自动处理JSON字段）
            $result = $this->where('id', $id)->update($data);
            return $result !== false;
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::updateEvent 更新失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 删除编年史事件
     * @param int $id
     * @return bool
     */
    public function deleteEvent($id)
    {
        try {
            // 软删除主记录 - 标签存储在JSON字段中，无需单独删除
            $result = self::destroy($id);
            
            trace('EconomicsHistory Model::deleteEvent 删除成功, id=' . $id, 'info');
            return $result !== false;
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::deleteEvent 删除失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 获取事件类型列表
     * @return array
     */
    public function getEventTypes()
    {
        try {
            $eventTypeModel = new EconomicsHistoryEventTypes();
            return $eventTypeModel->where('status', 1)
                ->order('sort_order asc, id asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::getEventTypes 查询失败: ' . $e->getMessage(), 'error');
            return [];
        }
    }

    /**
     * 创建事件类型
     * @param array $data
     * @return int|false
     */
    public function createEventType($data)
    {
        try {
            $eventTypeModel = new EconomicsHistoryEventTypes();
            $eventTypeModel->save($data);
            return $eventTypeModel->id;
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::createEventType 创建失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 更新事件类型
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateEventType($id, $data)
    {
        try {
            $eventTypeModel = new EconomicsHistoryEventTypes();
            $result = $eventTypeModel->where('id', $id)->update($data);
            return $result !== false;
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::updateEventType 更新失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 删除事件类型
     * @param int $id
     * @return bool
     */
    public function deleteEventType($id)
    {
        try {
            $eventTypeModel = new EconomicsHistoryEventTypes();
            $result = $eventTypeModel->where('id', $id)->delete();
            return $result !== false;
        } catch (\Exception $e) {
            trace('EconomicsHistory Model::deleteEventType 删除失败: ' . $e->getMessage(), 'error');
            return false;
        }
    }
}