<template>
  <div class="economics-history-page">
    <!-- 页面标题区 -->
    <div class="container">
      <n-card class="page-header" :bordered="false">
      <template #header>
        <div class="header-content-row">
          <!-- 左侧标题区 -->
          <div class="title-section">
            <div class="title-row">
              <n-icon :size="24" class="title-icon">
                <HistoryIcon />
              </n-icon>
              <h1>三角洲经济学编年史</h1>
            </div>
            <p class="subtitle">记录游戏经济系统的重要变迁时刻</p>
          </div>
          
          <!-- 中间筛选区 -->
          <div class="filter-section-middle" v-if="!loading">
            <n-space :wrap="false" class="filter-controls-horizontal">
              <n-input
                v-model:value="searchKeyword"
                placeholder="搜索事件..."
                clearable
                style="width: 200px"
              >
                <template #prefix>
                  <n-icon>
                    <SearchIcon />
                  </n-icon>
                </template>
              </n-input>

              <n-select
                v-model:value="filterEventType"
                :options="eventTypeOptions"
                placeholder="事件类型"
                clearable
                style="width: 120px"
              >
                <template #arrow>
                  <n-icon>
                    <ChevronDownIcon />
                  </n-icon>
                </template>
              </n-select>

              <n-select
                v-model:value="filterImportanceLevel"
                :options="importanceLevelOptions"
                placeholder="重要程度"
                clearable
                style="width: 100px"
              >
                <template #arrow>
                  <n-icon>
                    <ChevronDownIcon />
                  </n-icon>
                </template>
              </n-select>

              <n-button
                v-if="editMode"
                @click="handleAddEvent"
                type="primary"
                ghost
                size="small"
              >
                <template #icon>
                  <n-icon>
                    <AddIcon />
                  </n-icon>
                </template>
                新增
              </n-button>
            </n-space>
          </div>

          <!-- 右侧控制区 -->
          <div class="control-section">
            <n-space>
              <n-select
                v-model:value="currentSeason"
                :options="seasonOptions"
                placeholder="选择赛季"
                style="width: 100px"
                size="small"
                @update:value="onSeasonChange"
              >
                <template #arrow>
                  <n-icon>
                    <ChevronDownIcon />
                  </n-icon>
                </template>
              </n-select>
              <n-button
                v-if="!editMode"
                @click="enterEditMode"
                type="primary"
                size="small"
                :loading="verifyingPassword"
              >
                编辑模式
              </n-button>
              <n-button v-else @click="exitEditMode" type="default" size="small">
                退出编辑
              </n-button>

              <!-- 密码管理按钮 -->
              <n-dropdown :options="passwordMenuOptions" @select="handlePasswordMenuSelect">
                <n-button size="small" quaternary>
                  <template #icon>
                    <n-icon>
                      <svg viewBox="0 0 24 24" width="16" height="16">
                        <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"/>
                      </svg>
                    </n-icon>
                  </template>
                </n-button>
              </n-dropdown>
            </n-space>
          </div>
        </div>
      </template>

      <template #header-extra>
        <!-- 空的，所有内容都在header中 -->
      </template>
    </n-card>
    </div>


    <!-- 时间轴展示区（自研组件） -->
    <div class="container">
      <div class="timeline-section">
      <n-spin :show="loading" description="加载编年史数据中...">
        <div v-if="filteredEvents.length > 0" class="timeline-container">
          <D3TimelineView
            :events="filteredEvents"
            :edit-mode="editMode"
            @edit="handleEditEvent"
            @delete="handleDeleteEvent"
          />
        </div>

        <n-empty
          v-else-if="!loading"
          description="暂无编年史数据"
          style="margin: 40px 0"
        >
          <template #extra>
            <n-button
              v-if="editMode"
              @click="handleAddEvent"
              type="primary"
              ghost
            >
              立即添加第一个事件
            </n-button>
          </template>
        </n-empty>
      </n-spin>

      <!-- 错误提示 -->
      <n-alert
        v-if="error"
        type="error"
        :title="error"
        closable
        @close="clearError"
        style="margin-top: 16px"
      />
      </div>
    </div>

    <!-- 弹窗组件 -->
    <PasswordDialog
      v-model:show="showPasswordDialog"
      :saved-password="currentPassword"
      @success="onPasswordSuccess"
    />

    <EventEditor
      v-model:show="showEventEditor"
      :event="currentEvent"
      :season="currentSeason"
      :saved-password="currentPassword"
      @save="onEventSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import { useMessage, useDialog } from 'naive-ui'
import { HistoryIcon, SearchIcon, AddIcon, ChevronDownIcon } from '@/components/icons'
import * as economicsHistoryApi from '@/api/economicsHistory'
import type { EconomicsHistoryEvent } from '@/types/economicsHistory'
import {
  SEASON_OPTIONS,
  IMPORTANCE_LEVEL_OPTIONS,
  type EventType
} from '@/types/economicsHistory'

import D3TimelineView from './components/D3TimelineView.vue'
import EventEditor from './components/EventEditor.vue'
import PasswordDialog from './components/PasswordDialog.vue'
import { Storage } from '@/utils/storage'

const message = useMessage()
const dialog = useDialog()

// 响应式状态
const loading = ref(false)
const verifyingPassword = ref(false)
const error = ref<string | null>(null)
const events = ref<EconomicsHistoryEvent[]>([])
const editMode = ref(false)
const currentSeason = ref('S5')
const searchKeyword = ref('')
const filterEventType = ref('')
const filterImportanceLevel = ref<number | null>(null)
const showPasswordDialog = ref(false)
const showEventEditor = ref(false)
const currentEvent = ref<EconomicsHistoryEvent | null>(null)
const currentPassword = ref('')
const eventTypes = ref<EventType[]>([])
const loadingEventTypes = ref(false)

// 密码记忆功能
const PASSWORD_STORAGE_KEY = 'economics_history_edit_password'

// 加载保存的密码
const loadSavedPassword = () => {
  try {
    const saved = localStorage.getItem(PASSWORD_STORAGE_KEY)
    if (saved) {
      currentPassword.value = saved
    }
  } catch (error) {
    console.warn('加载保存的密码失败:', error)
  }
}

// 保存密码到本地存储
const savePassword = (password: string) => {
  try {
    if (password.trim()) {
      localStorage.setItem(PASSWORD_STORAGE_KEY, password.trim())
      currentPassword.value = password.trim()
    }
  } catch (error) {
    console.warn('保存密码失败:', error)
  }
}

// 选项配置
const seasonOptions = SEASON_OPTIONS
const importanceLevelOptions = IMPORTANCE_LEVEL_OPTIONS

// 密码管理菜单选项
const passwordMenuOptions = computed(() => {
  const hasSavedPassword = !!Storage.getEditCredentials()
  return [
    {
      label: hasSavedPassword ? '已保存密码' : '未保存密码',
      key: 'status',
      disabled: true,
      icon: () => h('span', hasSavedPassword ? '🔒' : '🔓')
    },
    {
      label: '清除保存的密码',
      key: 'clear',
      disabled: !hasSavedPassword,
      icon: () => h('span', '🗑️')
    }
  ]
})

// 动态事件类型选项
const eventTypeOptions = computed(() => {
  return eventTypes.value.map(type => ({
    label: type.type_display_name,
    value: type.type_name, // 使用type_name作为筛选值
    disabled: type.status === 0
  }))
})

// 计算属性 - 过滤后的事件列表
const filteredEvents = computed(() => {
  let result = [...events.value]

  // 搜索关键词过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(event =>
      event.title.toLowerCase().includes(keyword) ||
      event.description.toLowerCase().includes(keyword) ||
      event.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }

  // 事件类型过滤
  if (filterEventType.value) {
    result = result.filter(event => {
      // 支持预设类型和自定义类型的筛选
      const eventTypeName = event.event_type?.type_name || event.event_type_custom
      return eventTypeName === filterEventType.value
    })
  }

  // 重要程度过滤
  if (filterImportanceLevel.value !== null) {
    result = result.filter(event => event.importance_level >= filterImportanceLevel.value!)
  }

  // 按日期升序排列（最早的在前）
  return result.sort((a, b) => {
    try {
      // 确保日期字符串有效
      if (!a.event_date || !b.event_date) {
        return a.event_date ? -1 : b.event_date ? 1 : 0
      }
      
      const dateA = new Date(a.event_date + ' ' + (a.event_time || '00:00:00'))
      const dateB = new Date(b.event_date + ' ' + (b.event_time || '00:00:00'))
      
      // 检查日期是否有效
      if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
        console.warn('排序时遇到无效日期:', 
          isNaN(dateA.getTime()) ? a : null,
          isNaN(dateB.getTime()) ? b : null
        )
        return isNaN(dateA.getTime()) ? 1 : isNaN(dateB.getTime()) ? -1 : 0
      }
      
      return dateA.getTime() - dateB.getTime()
    } catch (error) {
      console.warn('事件排序时发生错误:', error, a, b)
      return 0
    }
  })
})

// 方法
const loadSeasonEvents = async (season: string) => {
  loading.value = true
  error.value = null

  try {
    const response = await economicsHistoryApi.getSeasonHistory(season)
    if (response.code === 1) {
      events.value = response.data || []
    } else {
      throw new Error(response.msg || '获取数据失败')
    }
  } catch (err: any) {
    error.value = err.message || '加载编年史失败'
    console.error('加载失败:', err)
    events.value = []
  } finally {
    loading.value = false
  }
}

const onSeasonChange = (season: string) => {
  currentSeason.value = season
  loadSeasonEvents(season)
}

const enterEditMode = () => {
  // 首先尝试获取本地保存的密码
  const savedPassword = Storage.getEditCredentials()

  if (savedPassword) {
    // 如果有保存的密码，直接使用
    console.log('使用本地保存的密码进入编辑模式')
    currentPassword.value = savedPassword
    editMode.value = true
    message.success('已使用保存的密码进入编辑模式')
  } else {
    // 如果没有保存的密码，显示密码输入对话框
    showPasswordDialog.value = true
  }
}

const exitEditMode = () => {
  editMode.value = false
  currentPassword.value = ''

  // 询问用户是否清除保存的密码
  dialog.info({
    title: '退出编辑模式',
    content: '是否清除本地保存的密码？清除后下次需要重新输入密码。',
    positiveText: '清除密码',
    negativeText: '保留密码',
    onPositiveClick: () => {
      Storage.clearEditCredentials()
      message.info('已退出编辑模式并清除保存的密码')
    },
    onNegativeClick: () => {
      message.info('已退出编辑模式，密码已保留')
    }
  })
}

const handleEditEvent = (event: EconomicsHistoryEvent) => {
  currentEvent.value = event
  showEventEditor.value = true
}

const handleDeleteEvent = (event: EconomicsHistoryEvent) => {
  // 检查是否有保存的密码
  if (!currentPassword.value) {
    message.error('请先通过密码验证进入编辑模式')
    return
  }

  dialog.warning({
    title: '删除确认',
    content: `确定要删除事件："${event.title}" 吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const response = await economicsHistoryApi.deleteEvent({
          id: event.id,
          password: currentPassword.value
        })
        
        if (response.code === 1) {
          message.success('删除成功')
          // 清理前端缓存
          Storage.clearEconomicsHistoryCache()
          await loadSeasonEvents(currentSeason.value)
        } else {
          throw new Error(response.msg || '删除失败')
        }
      } catch (error: any) {
        console.error('删除失败:', error)
        message.error(error.message || '删除失败')
        // 如果是认证错误，可能需要重新验证密码
        if (error.message?.includes('认证失败')) {
          editMode.value = false
          message.info('密码可能已过期，请重新验证')
        }
      }
    }
  })
}

const handleAddEvent = () => {
  currentEvent.value = null
  showEventEditor.value = true
}

const onPasswordSuccess = (password: string) => {
  showPasswordDialog.value = false
  editMode.value = true
  savePassword(password) // 保存验证成功的密码
  // 同时保存到本地存储，供下次自动使用
  Storage.saveEditCredentials(password)
  message.success('验证成功，已进入编辑模式（密码已保存）')
}

const onEventSave = async () => {
  showEventEditor.value = false
  // 清理前端缓存
  Storage.clearEconomicsHistoryCache()
  await loadSeasonEvents(currentSeason.value)
}

const clearError = () => {
  error.value = null
}

// 处理密码菜单选择
const handlePasswordMenuSelect = (key: string) => {
  if (key === 'clear') {
    dialog.warning({
      title: '清除保存的密码',
      content: '确定要清除本地保存的密码吗？清除后下次进入编辑模式需要重新输入密码。',
      positiveText: '确定清除',
      negativeText: '取消',
      onPositiveClick: () => {
        Storage.clearEditCredentials()
        message.success('已清除保存的密码')
      }
    })
  }
}

// 加载事件类型列表
const loadEventTypes = async () => {
  if (loadingEventTypes.value) return
  
  loadingEventTypes.value = true
  try {
    const response = await economicsHistoryApi.getEventTypeList()
    if (response.code === 1) {
      eventTypes.value = response.data || []
    } else {
      console.warn('获取事件类型失败:', response.msg)
      eventTypes.value = []
    }
  } catch (error: any) {
    console.error('加载事件类型失败:', error)
    eventTypes.value = []
  } finally {
    loadingEventTypes.value = false
  }
}

// 生命周期
onMounted(() => {
  loadSavedPassword() // 加载保存的密码
  loadEventTypes() // 加载事件类型列表
  loadSeasonEvents(currentSeason.value)
})
</script>

<style scoped>
@import '@/styles/components/EconomicsHistory.css';

/* 页面容器：采用与其他页面一致的布局 */
.economics-history-page {
  width: 100%;
  padding: 0;
  margin: 0;
  background: var(--n-body-color);
}

/* 容器布局 - 全宽无限制 */
.container {
  width: 100%;
  padding: 0 16px;
}

/* 页面区块间距 */
.page-header {
  margin-bottom: 16px;
}

/* 横向布局的header内容 */
.header-content-row {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  width: 100%;
  min-height: 60px;
}

/* 左侧标题区域 */
.title-section {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: var(--primary-color);
  flex-shrink: 0;
}

.title-section h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color-1);
  white-space: nowrap;
}

.subtitle {
  margin: 0;
  color: var(--text-color-2);
  font-size: 0.875rem;
  white-space: nowrap;
}

/* 中间筛选区域 */
.filter-section-middle {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
}

.filter-controls-horizontal {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: nowrap;
}

/* 右侧控制区域 */
.control-section {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 去除 n-card 对时间轴容器的包裹，改为普通 div 后保留最小高度 */
.timeline-section {
  min-height: 400px;
}

.timeline-container {
  padding: 20px 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .header-content-row {
    gap: 16px;
  }
  
  .filter-controls-horizontal {
    gap: 8px;
  }
  
  .filter-section-middle input,
  .filter-section-middle .n-select {
    width: auto !important;
    min-width: 120px !important;
  }
}

@media (max-width: 992px) {
  .header-content-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-section-middle {
    width: 100%;
    justify-content: flex-start;
  }
  
  .control-section {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 12px;
  }

  .filter-controls-horizontal {
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-controls-horizontal > * {
    flex: 1 1 auto;
    min-width: 120px !important;
  }

  .title-section h1 {
    font-size: 1.25rem;
  }

  .control-section .n-space {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 640px) {
  .timeline-container {
    padding: 12px 0;
  }
}
:deep(.n-layout){
  min-height: 0px;
}

</style>