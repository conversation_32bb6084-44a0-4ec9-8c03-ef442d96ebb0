<template>
  <n-modal
    v-model:show="visible"
    :mask-closable="false"
    preset="card"
    style="width: 90%; max-width: 600px;"
    :title="isEdit ? '编辑事件' : '新增事件'"
    :segmented="{ content: true, footer: 'soft' }"
    @after-leave="resetForm"
  >
    <n-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="top"
      label-width="auto"
      require-mark-placement="right-hanging"
    >
      <!-- 基本信息 -->
      <n-grid :cols="2" :x-gap="16" :y-gap="16" responsive="screen">
        <n-gi span="2">
          <n-form-item label="事件标题" path="title">
            <n-input
              v-model:value="formData.title"
              placeholder="请输入事件标题"
              :maxlength="200"
              show-count
            />
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="事件类型" path="event_type_id">
            <div class="event-type-selector">
              <n-select
                v-model:value="formData.event_type_id"
                :options="eventTypeOptions"
                :loading="loadingEventTypes"
                placeholder="选择事件类型"
                style="flex: 1"
              >
                <template #arrow>
                  <n-icon>
                    <ChevronDownIcon />
                  </n-icon>
                </template>
              </n-select>
              <n-button
                type="primary"
                ghost
                @click="showEventTypeManager = true"
                style="margin-left: 8px"
              >
                管理类型
              </n-button>
            </div>
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="卡片位置" path="card_position">
            <n-select
              v-model:value="formData.card_position"
              :options="cardPositionOptions"
              placeholder="选择卡片显示位置"
              clearable
            >
              <template #arrow>
                <n-icon>
                  <ChevronDownIcon />
                </n-icon>
              </template>
            </n-select>
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="重要程度" path="importance_level">
            <n-select
              v-model:value="formData.importance_level"
              :options="importanceLevelOptions"
              placeholder="选择重要程度"
            >
              <template #arrow>
                <n-icon>
                  <ChevronDownIcon />
                </n-icon>
              </template>
            </n-select>
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="事件日期" path="dateString">
            <n-date-picker
              :value="parseDateValue(formData.dateString)"
              @update:value="handleDateChange"
              type="date"
              placeholder="选择事件日期"
              format="yyyy-MM-dd"
              style="width: 100%"
              clearable
            >
              <template #arrow>
                <n-icon>
                  <ChevronDownIcon />
                </n-icon>
              </template>
            </n-date-picker>
          </n-form-item>
        </n-gi>

        <n-gi>
          <n-form-item label="具体时间" path="event_time">
            <n-time-picker
              :value="parseTimeValue(formData.event_time)"
              @update:value="handleTimeChange"
              format="HH:mm:ss"
              placeholder="选择具体时间（可选）"
              style="width: 100%"
              clearable
            >
              <template #arrow>
                <n-icon>
                  <ChevronDownIcon />
                </n-icon>
              </template>
            </n-time-picker>
          </n-form-item>
        </n-gi>

        <n-gi span="2">
          <n-form-item label="事件描述" path="description">
            <n-input
              v-model:value="formData.description"
              type="textarea"
              placeholder="请详细描述这个事件的内容和影响..."
              :autosize="{ minRows: 4, maxRows: 8 }"
              :maxlength="2000"
              show-count
            />
          </n-form-item>
        </n-gi>

        <n-gi span="2">
          <n-form-item label="事件标签" path="tags">
            <n-dynamic-tags
              v-model:value="formData.tags"
              :max="10"
              :trigger="['enter', 'space']"
              placeholder="输入标签后按Enter或空格添加"
            />
          </n-form-item>
        </n-gi>

        <n-gi span="2">
          <n-form-item label="编辑密码" path="password">
            <n-input
              v-model:value="formData.password"
              type="password"
              placeholder="请输入编辑密码"
              show-password-on="mousedown"
            />
            <template #feedback>
              <div style="font-size: 12px; color: var(--n-text-color-3); margin-top: 4px;">
                密码已自动填入，如操作失败请检查密码是否正确
              </div>
            </template>
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleCancel">取消</n-button>
        <n-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isEdit ? '更新' : '创建' }}
        </n-button>
      </n-space>
    </template>
  </n-modal>

  <!-- 事件类型管理器 -->
  <EventTypeManager
    v-model:show="showEventTypeManager"
    :password="formData.password"
    @refresh="loadEventTypes"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useMessage, type FormInst, type FormRules } from 'naive-ui'
import { ChevronDownIcon } from '@/components/icons'
import EventTypeManager from './EventTypeManager.vue'
import { Storage } from '@/utils/storage'
import type { 
  EconomicsHistoryEvent, 
  EventType,
  CreateEventRequest, 
  UpdateEventRequest,
  EventTypeOption
} from '@/types/economicsHistory'
import { 
  IMPORTANCE_LEVEL_OPTIONS 
} from '@/types/economicsHistory'
import * as economicsHistoryApi from '@/api/economicsHistory'

interface Props {
  show: boolean
  event?: EconomicsHistoryEvent | null
  season: string
  savedPassword?: string
}

const props = withDefaults(defineProps<Props>(), {
  event: null,
  savedPassword: ''
})

const emit = defineEmits<{
  'update:show': [value: boolean]
  'save': []
}>()

const message = useMessage()

// 响应式状态
const visible = ref(props.show)
const submitting = ref(false)
const formRef = ref<FormInst | null>(null)
const eventTypes = ref<EventType[]>([])
const loadingEventTypes = ref(false)
const showEventTypeManager = ref(false)

// 计算属性
const isEdit = computed(() => !!props.event)

// 获取今天的日期字符串
const getTodayString = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 加载事件类型
const loadEventTypes = async () => {
  if (loadingEventTypes.value) return
  
  try {
    loadingEventTypes.value = true
    const response = await economicsHistoryApi.getEventTypeList()
    if (response.code === 1) {
      eventTypes.value = response.data
    } else {
      message.error('加载事件类型失败')
    }
  } catch (error) {
    message.error('加载事件类型失败')
  } finally {
    loadingEventTypes.value = false
  }
}

// 表单数据 - 完全使用字符串避免任何日期对象
const formData = reactive({
  title: '',
  event_type_id: null as number | null,
  importance_level: 3 as number,
  dateString: '', // 直接使用字符串
  event_time: '',
  description: '',
  tags: [] as string[],
  card_position: 'auto' as 'above' | 'below' | 'auto', // 卡片位置
  password: ''
})

// 选项配置
const importanceLevelOptions = IMPORTANCE_LEVEL_OPTIONS

// 卡片位置选项 - 前端写死
const cardPositionOptions = [
  { value: 'auto', label: '自动选择位置' },
  { value: 'above', label: '固定在时间轴上方' },
  { value: 'below', label: '固定在时间轴下方' }
]

// 计算事件类型选项
const eventTypeOptions = computed(() => {
  return eventTypes.value.map(type => ({
    label: type.type_display_name,
    value: type.id,
    color: type.type_color,
    icon: type.type_icon
  }))
})

// 日期值解析函数
const parseDateValue = (dateStr: string): number | null => {
  if (!dateStr) return null
  try {
    const date = new Date(dateStr)
    return isNaN(date.getTime()) ? null : date.getTime()
  } catch {
    return null
  }
}

// 时间值解析函数
const parseTimeValue = (timeStr: string): number | null => {
  if (!timeStr) return null
  try {
    // 使用今天的日期 + 时间来创建时间戳
    const today = new Date().toISOString().split('T')[0]
    const dateTimeStr = `${today}T${timeStr}`
    const date = new Date(dateTimeStr)
    return isNaN(date.getTime()) ? null : date.getTime()
  } catch {
    return null
  }
}

// 处理日期变化
const handleDateChange = (value: number | null) => {
  if (value) {
    const date = new Date(value)
    formData.dateString = date.toISOString().split('T')[0]
  } else {
    formData.dateString = ''
  }
}

// 处理时间变化
const handleTimeChange = (value: number | null) => {
  if (value) {
    const date = new Date(value)
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    formData.event_time = `${hours}:${minutes}:${seconds}`
  } else {
    formData.event_time = ''
  }
}

// 日期验证函数
const validateDate = (dateStr: string): boolean => {
  if (!dateStr) return false
  const regex = /^\d{4}-\d{2}-\d{2}$/
  if (!regex.test(dateStr)) return false
  
  const [year, month, day] = dateStr.split('-').map(Number)
  const date = new Date(year, month - 1, day)
  return date.getFullYear() === year && 
         date.getMonth() === month - 1 && 
         date.getDate() === day
}

// 时间验证函数
const validateTime = (timeStr: string): boolean => {
  if (!timeStr) return true // 时间是可选的
  const regex = /^\d{2}:\d{2}:\d{2}$/
  if (!regex.test(timeStr)) return false
  
  const [hour, minute, second] = timeStr.split(':').map(Number)
  return hour >= 0 && hour <= 23 && 
         minute >= 0 && minute <= 59 && 
         second >= 0 && second <= 59
}

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入事件标题', trigger: 'blur' },
    { min: 2, max: 200, message: '标题长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  event_type_id: [
    { required: false, type: 'number', message: '请选择事件类型', trigger: 'change' }
  ],
  event_type_custom: [
    { required: false, message: '请输入自定义事件类型', trigger: 'blur' }
  ],
  importance_level: [
    { required: true, type: 'number', message: '请选择重要程度', trigger: 'change' }
  ],
  dateString: [
    { required: true, message: '请输入事件日期', trigger: 'blur' },
    { validator: (rule, value) => validateDate(value), message: '请输入有效的日期格式 YYYY-MM-DD', trigger: 'blur' }
  ],
  event_time: [
    { validator: (rule, value) => validateTime(value), message: '请输入有效的时间格式 HH:mm:ss', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入事件描述', trigger: 'blur' },
    { max: 2000, message: '描述长度不能超过 2000 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入编辑密码', trigger: 'blur' }
  ]
}

// 方法
const loadEventData = () => {
  if (props.event) {
    // 编辑模式：加载现有事件数据
    formData.title = props.event.title
    formData.event_type_id = props.event.event_type_id || null
    formData.importance_level = props.event.importance_level
    formData.dateString = props.event.event_date || ''
    formData.event_time = props.event.event_time || ''
    formData.description = props.event.description
    formData.tags = [...(props.event.tags || [])]
    formData.card_position = (props.event as any).card_position || 'auto'
  } else {
    // 新增模式：设置默认值
    formData.title = ''
    formData.event_type_id = null
    formData.importance_level = 3
    formData.dateString = getTodayString()
    formData.event_time = ''
    formData.description = ''
    formData.tags = []
    formData.card_position = 'above'
  }
}

const resetForm = () => {
  formData.title = ''
  formData.event_type_id = null
  formData.importance_level = 3
  formData.dateString = ''
  formData.event_time = ''
  formData.description = ''
  formData.tags = []
  formData.card_position = 'above'
  formData.password = ''
  
  nextTick(() => {
    formRef.value?.restoreValidation()
  })
}

const handleCancel = () => {
  visible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      title: formData.title.trim(),
      event_type_id: formData.event_type_id || undefined,
      importance_level: formData.importance_level,
      event_date: formData.dateString,
      event_time: formData.event_time || undefined,
      description: formData.description.trim(),
      tags: formData.tags.filter(tag => tag.trim()),
      card_position: formData.card_position,
      password: formData.password
    }

    if (isEdit.value && props.event) {
      // 更新事件
      const updateData: UpdateEventRequest = {
        id: props.event.id,
        ...submitData
      }
      await economicsHistoryApi.updateEvent(updateData)
      message.success('事件更新成功')
    } else {
      // 创建新事件
      const createData: CreateEventRequest = {
        season: props.season,
        ...submitData
      }
      await economicsHistoryApi.createEvent(createData)
      message.success('事件创建成功')
    }

    visible.value = false
    emit('save')
  } catch (error: any) {
    message.error(error.message || (isEdit.value ? '更新失败' : '创建失败'))
  } finally {
    submitting.value = false
  }
}

// 监听器
watch(() => props.show, (newValue) => {
  visible.value = newValue
  if (newValue) {
    nextTick(() => {
      loadEventTypes() // 加载事件类型
      loadEventData()
      // 自动获取本地保存的密码
      const savedPassword = Storage.getEditCredentials()
      if (savedPassword && !formData.password) {
        formData.password = savedPassword
        console.log('已自动填入保存的密码')
      } else if (props.savedPassword) {
        // 兼容原有的props传递方式
        formData.password = props.savedPassword
      }
    })
  }
})

watch(visible, (newValue) => {
  emit('update:show', newValue)
})
</script>

<style scoped>
.event-type-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}
:deep(.n-card-header) {
  padding-bottom: 16px;
}

:deep(.n-card__content) {
  padding-top: 0;
}

:deep(.n-form-item-label) {
  font-weight: 500;
}

:deep(.n-dynamic-tags) {
  min-height: 34px;
}

/* 响应式调整 */
@media (max-width: 640px) {
  :deep(.n-modal) {
    width: 95% !important;
    margin: 16px;
  }
  
  :deep(.n-grid) {
    grid-template-columns: 1fr !important;
  }
  
  :deep(.n-gi[span="2"]) {
    grid-column: span 1;
  }
}
</style>