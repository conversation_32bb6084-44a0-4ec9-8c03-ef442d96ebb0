// 三角洲经济学编年史相关类型定义

export interface EconomicsHistoryEvent {
  id: number;
  season: string;
  event_date: string;
  event_time?: string;
  title: string;
  description: string;
  event_type_id?: number;
  event_type_custom?: string;
  event_type_color?: string;
  event_type?: EventType; // 关联的事件类型对象
  importance_level: number;
  tags: string[];
  card_position?: 'above' | 'below' | 'auto'; // 卡片显示位置：上方、下方或自动
  create_time: string;
  update_time: string;
}

export interface EventType {
  id: number;
  type_name: string;
  type_display_name: string;
  type_color: string;
  type_icon: string;
  sort_order: number;
  status: number;
  create_time: string;
  update_time?: string;
}

export interface EconomicsHistoryFilter {
  season: string;
  event_type?: string;
  importance_level?: number;
  start_date?: string;
  end_date?: string;
}

export interface CreateEventRequest {
  password: string;
  season: string;
  event_date: string;
  event_time?: string;
  title: string;
  description: string;
  event_type_id?: number;
  event_type_custom?: string;
  event_type_color?: string;
  importance_level: number;
  tags: string[];
  card_position?: 'above' | 'below' | 'auto';
}

export interface UpdateEventRequest {
  password: string;
  id: number;
  title?: string;
  description?: string;
  event_date?: string;
  event_time?: string;
  event_type_id?: number;
  event_type_custom?: string;
  event_type_color?: string;
  importance_level?: number;
  tags?: string[];
  card_position?: 'above' | 'below' | 'auto';
}

export interface CreateEventTypeRequest {
  password: string;
  type_name: string;
  type_display_name: string;
  type_color?: string;
  type_icon?: string;
  sort_order?: number;
}

export interface UpdateEventTypeRequest {
  password: string;
  id: number;
  type_display_name?: string;
  type_color?: string;
  type_icon?: string;
  sort_order?: number;
  status?: number;
}

export interface DeleteEventTypeRequest {
  password: string;
  id: number;
}

export interface DeleteEventRequest {
  password: string;
  id: number;
}

export interface EventTypeOption {
  label: string;
  value: number;
  color: string;
  icon: string;
}

// 向后兼容的旧式事件类型选项（已废弃，请使用动态API获取）
export interface LegacyEventTypeOption {
  label: string;
  value: string;
}

// @deprecated 使用动态API获取事件类型，此常量仅为向后兼容保留
export const EVENT_TYPE_OPTIONS: LegacyEventTypeOption[] = [
  { label: '赛季开始', value: 'season_start' },
  { label: '游戏更新', value: 'update' },
  { label: '市场变动', value: 'market' },
  { label: '政策调整', value: 'policy' },
  { label: 'Bug修复', value: 'bug_fix' },
  { label: '价格调整', value: 'price_adjustment' },
  { label: '新物品上线', value: 'new_item' },
  { label: '平衡性调整', value: 'balance_change' },
  { label: '重大事件', value: 'major_event' }
]

export interface SeasonOption {
  label: string;
  value: string;
}

export interface ImportanceLevelOption {
  label: string;
  value: number;
}

// 重要程度选项
export const IMPORTANCE_LEVEL_OPTIONS: ImportanceLevelOption[] = [
  { label: '1级', value: 1 },
  { label: '2级', value: 2 },
  { label: '3级', value: 3 },
  { label: '4级', value: 4 },
  { label: '5级', value: 5 }
]

// 赛季选项
export const SEASON_OPTIONS: SeasonOption[] = [
  { label: 'S5赛季', value: 'S5' },
  { label: 'S4赛季', value: 'S4' },
  { label: 'S3赛季', value: 'S3' }
]

// 默认事件类型颜色映射（兼容性保留）
export const EVENT_TYPE_COLORS: Record<string, string> = {
  'season_start': '#52c41a',    // 绿色 - 赛季开始
  'update': '#1890ff',          // 蓝色 - 游戏更新
  'market': '#fa8c16',          // 橙色 - 市场变动
  'policy': '#722ed1',          // 紫色 - 政策调整
  'bug_fix': '#f5222d',         // 红色 - Bug修复
  'price_adjustment': '#fa541c', // 橙红色 - 价格调整
  'new_item': '#13c2c2',        // 青色 - 新物品上线
  'balance_change': '#52c41a',  // 绿色 - 平衡性调整
  'major_event': '#eb2f96',     // 粉色 - 重大事件
  'other': '#909399'            // 灰色 - 其他
}

// 获取事件显示类型的工具函数
export function getEventDisplayType(event: EconomicsHistoryEvent): string {
  if (event.event_type?.type_display_name) {
    return event.event_type.type_display_name
  }
  if (event.event_type_custom) {
    return event.event_type_custom
  }
  return '未知类型'
}

// 获取事件类型颜色的工具函数
export function getEventTypeColor(event: EconomicsHistoryEvent): string {
  if (event.event_type?.type_color) {
    return event.event_type.type_color
  }
  if (event.event_type_color) {
    return event.event_type_color
  }
  return '#1890ff' // 默认蓝色
}