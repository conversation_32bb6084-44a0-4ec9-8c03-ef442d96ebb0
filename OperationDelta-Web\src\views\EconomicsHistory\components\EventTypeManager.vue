<template>
  <n-modal
    v-model:show="visible"
    preset="card"
    title="事件类型管理"
    :style="{ width: '800px', maxWidth: '90vw' }"
    :mask-closable="false"
    @after-leave="resetForm"
  >
    <div class="event-type-manager">
      <!-- 添加新类型 -->
      <n-card title="添加新类型" size="small" style="margin-bottom: 16px">
        <n-form
          ref="formRef"
          :model="newTypeForm"
          :rules="rules"
          inline
          label-placement="left"
        >
          <n-form-item label="类型名称" path="type_display_name">
            <n-input
              v-model:value="newTypeForm.type_display_name"
              placeholder="输入类型名称"
              style="width: 150px"
            />
          </n-form-item>
          <n-form-item label="颜色" path="type_color">
            <n-color-picker
              v-model:value="newTypeForm.type_color"
              :modes="['hex']"
              :show-alpha="false"
              style="width: 80px"
            />
          </n-form-item>
          <n-form-item>
            <n-button
              type="primary"
              @click="handleAddType"
              :loading="adding"
            >
              添加
            </n-button>
          </n-form-item>
        </n-form>
      </n-card>

      <!-- 现有类型列表 -->
      <n-card title="现有类型" size="small">
        <n-data-table
          :columns="columns"
          :data="eventTypes"
          :loading="loading"
          :pagination="false"
          size="small"
        />
      </n-card>
    </div>

    <template #footer>
      <n-space justify="end">
        <n-button @click="handleClose">关闭</n-button>
      </n-space>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, h } from 'vue'
import { useMessage, useDialog, type FormInst, type FormRules, type DataTableColumns } from 'naive-ui'
import { NButton, NTag, NSpace, NPopconfirm } from 'naive-ui'
import * as economicsHistoryApi from '@/api/economicsHistory'
import type { EventType } from '@/types/economicsHistory'
import { Storage } from '@/utils/storage'

// Props
interface Props {
  show: boolean
  password: string
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  password: ''
})

// Events
const emit = defineEmits<{
  'update:show': [value: boolean]
  refresh: []
}>()

// 响应式状态
const visible = ref(props.show)
const loading = ref(false)
const adding = ref(false)
const formRef = ref<FormInst | null>(null)
const eventTypes = ref<EventType[]>([])

const message = useMessage()
const dialog = useDialog()

// 新类型表单
const newTypeForm = reactive({
  type_display_name: '',
  type_color: '#1890ff',
  password: ''
})

// 表单验证规则
const rules: FormRules = {
  type_display_name: [
    { required: true, message: '请输入类型名称', trigger: 'blur' },
    { min: 1, max: 50, message: '类型名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 表格列定义
const columns: DataTableColumns<EventType> = [
  {
    title: '类型名称',
    key: 'type_display_name',
    render: (row) => h(NTag, { 
      color: { color: row.type_color, textColor: '#fff' } 
    }, { default: () => row.type_display_name })
  },
  {
    title: '颜色',
    key: 'type_color',
    width: 100,
    render: (row) => h('div', {
      style: {
        width: '20px',
        height: '20px',
        backgroundColor: row.type_color,
        borderRadius: '4px',
        border: '1px solid #d9d9d9'
      }
    })
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render: (row) => row.created_at ? new Date(row.created_at).toLocaleString('zh-CN') : '-'
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row) => h(NSpace, { size: 'small' }, {
      default: () => [
        h(NButton, {
          size: 'small',
          type: 'error',
          ghost: true,
          onClick: () => handleDeleteType(row)
        }, { default: () => '删除' })
      ]
    })
  }
]

// 监听props变化
watch(() => props.show, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadEventTypes()
  }
})

watch(visible, (newVal) => {
  emit('update:show', newVal)
})

// 加载事件类型
const loadEventTypes = async () => {
  loading.value = true
  try {
    const response = await economicsHistoryApi.getEventTypeList()
    if (response.code === 1) {
      eventTypes.value = response.data || []
    } else {
      message.error('加载事件类型失败')
    }
  } catch (error) {
    message.error('加载事件类型失败')
  } finally {
    loading.value = false
  }
}

// 添加新类型
const handleAddType = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (!props.password) {
      message.error('请先在事件编辑器中输入密码')
      return
    }
    
    adding.value = true
    
    const response = await economicsHistoryApi.createEventType({
      type_display_name: newTypeForm.type_display_name,
      type_color: newTypeForm.type_color,
      password: props.password
    })
    
    if (response.code === 1) {
      message.success('添加成功')
      // 清理缓存
      Storage.clearEconomicsHistoryCache()
      // 重新加载列表
      await loadEventTypes()
      // 通知父组件刷新
      emit('refresh')
      // 重置表单
      resetForm()
    } else {
      throw new Error(response.msg || '添加失败')
    }
  } catch (error: any) {
    message.error(error.message || '添加失败')
  } finally {
    adding.value = false
  }
}

// 删除类型
const handleDeleteType = (eventType: EventType) => {
  if (!props.password) {
    message.error('请先在事件编辑器中输入密码')
    return
  }
  
  dialog.warning({
    title: '删除确认',
    content: `确定要删除事件类型"${eventType.type_display_name}"吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const response = await economicsHistoryApi.deleteEventType({
          id: eventType.id,
          password: props.password
        })
        
        if (response.code === 1) {
          message.success('删除成功')
          // 清理缓存
          Storage.clearEconomicsHistoryCache()
          // 重新加载列表
          await loadEventTypes()
          // 通知父组件刷新
          emit('refresh')
        } else {
          throw new Error(response.msg || '删除失败')
        }
      } catch (error: any) {
        message.error(error.message || '删除失败')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  newTypeForm.type_display_name = ''
  newTypeForm.type_color = '#1890ff'
  formRef.value?.restoreValidation()
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.event-type-manager {
  max-height: 600px;
  overflow-y: auto;
}
</style>
