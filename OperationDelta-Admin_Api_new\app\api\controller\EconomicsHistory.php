<?php
namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\model\EconomicsHistory as EconomicsHistoryModel;
use app\api\service\ResponseAdapter;
use think\facade\Validate;
use think\facade\Cache;
use app\api\service\CacheManager;

/**
 * 经济学编年史控制器
 */
class EconomicsHistory extends Frontend
{
    // 无需登录的方法
    protected array $noNeedLogin = ['getSeasonHistory', 'getSeasonList', 'getEventTypeList', 'createEventType', 'updateEventType', 'deleteEventType', 'verifyPassword','deleteEvent','createEvent','updateEvent'];
    protected array $noNeedPermission = [];
    
    // 固定密码常量
    const EDIT_PASSWORD = 'dfhub1haozi2025';

    // 防爆破常量
    // 在窗口期内（秒）最多允许失败次数，超过后锁定一定时长
    private const THROTTLE_WINDOW = 300;        // 失败计数窗口：5分钟
    private const THROTTLE_MAX_FAILED = 5;      // 窗口期最大失败次数
    private const THROTTLE_LOCK_TTL = 600;      // 锁定时长：10分钟

    /**
     * 获取当前请求IP
     */
    private function getClientIp(): string
    {
        try {
            return (string)request()->ip();
        } catch (\Throwable $e) {
            return 'unknown';
        }
    }

    /**
     * 获取防爆破相关缓存键
     */
    private function getThrottleKeys(): array
    {
        $ip = $this->getClientIp();
        return [
            'count' => "eh:fail:" . $ip,
            'lock'  => "eh:lock:" . $ip,
        ];
    }

    /**
     * 是否处于锁定期
     */
    private function isIpLocked(): bool
    {
        $keys = $this->getThrottleKeys();
        return Cache::store('redis')->has($keys['lock']);
    }

    /**
     * 记录一次失败尝试，如达到阈值则锁定
     */
    private function recordFailedAttempt(): void
    {
        $keys = $this->getThrottleKeys();
        if (!Cache::store('redis')->has($keys['count'])) {
            Cache::store('redis')->set($keys['count'], 0, self::THROTTLE_WINDOW);
        }
        $count = (int)Cache::store('redis')->inc($keys['count']);
        // 保持窗口过期时间
        Cache::store('redis')->expire($keys['count'], self::THROTTLE_WINDOW);
        if ($count >= self::THROTTLE_MAX_FAILED) {
            Cache::store('redis')->set($keys['lock'], 1, self::THROTTLE_LOCK_TTL);
            trace(sprintf('EconomicsHistory throttle lock: ip=%s count=%d', $this->getClientIp(), $count), 'warning');
        }
    }

    /**
     * 清除失败计数与锁定
     */
    private function clearFailedAttempts(): void
    {
        $keys = $this->getThrottleKeys();
        Cache::store('redis')->delete($keys['count']);
        Cache::store('redis')->delete($keys['lock']);
    }

    /**
     * 清理所有经济学编年史相关缓存
     * 包括所有赛季数据、事件类型列表等
     */
    private function clearAllEconomicsHistoryCache(): void
    {
        try {
            $cache = new CacheManager();
            
            // 1. 清理所有赛季的编年史缓存
            $seasonList = ['S1', 'S2', 'S3', 'S4', 'S5', 'S6', 'S7', 'S8', 'S9', 'S10'];
            foreach ($seasonList as $season) {
                $cacheKey = 'economics_history:' . $season;
                $cache->delete($cacheKey, CacheManager::TYPE_DYNAMIC_DATA);
                trace("EconomicsHistory 清除缓存: {$cacheKey}", 'info');
            }
            
            // 2. 清理事件类型列表缓存
            $eventTypeCacheKey = 'economics_history:event_types';
            $cache->delete($eventTypeCacheKey, CacheManager::TYPE_STATIC_DATA);
            trace("EconomicsHistory 清除缓存: {$eventTypeCacheKey}", 'info');
            
            // 3. 清理赛季列表缓存
            $seasonListCacheKey = 'economics_history:season_list';
            $cache->delete($seasonListCacheKey, CacheManager::TYPE_STATIC_DATA);
            trace("EconomicsHistory 清除缓存: {$seasonListCacheKey}", 'info');
            
            // 4. 清理统计相关缓存
            $statsCacheKeys = [
                'economics_history:stats:total_events',
                'economics_history:stats:events_by_season',
                'economics_history:stats:events_by_type',
                'economics_history:stats:recent_events'
            ];
            
            foreach ($statsCacheKeys as $statsKey) {
                $cache->delete($statsKey, CacheManager::TYPE_DYNAMIC_DATA);
                trace("EconomicsHistory 清除统计缓存: {$statsKey}", 'info');
            }
            
            trace('EconomicsHistory 所有相关缓存已清理完成', 'info');
        } catch (\Exception $e) {
            trace('EconomicsHistory 缓存清理异常: ' . $e->getMessage(), 'error');
        }
    }

    /**
     * 校验编辑密码（包含防爆破）
     */
    private function verifyEditPassword(): bool
    {
        if ($this->isIpLocked()) {
            return false;
        }
        $password = $this->request->param('password');
        // 常量对比
        if (!hash_equals((string)self::EDIT_PASSWORD, (string)$password)) {
            $this->recordFailedAttempt();
            return false;
        }
        // 成功清理失败计数
        $this->clearFailedAttempts();
        return true;
    }

    /**
     * 获取指定赛季编年史
     */
    public function getSeasonHistory()
    {
        $season = $this->request->param('season', 'S5');
        
        // 记录请求参数
        trace('EconomicsHistory::getSeasonHistory 请求参数: season=' . $season, 'info');
        
        // 参数验证
        if (!preg_match('/^[sS]\d+$/', $season)) {
            trace('EconomicsHistory::getSeasonHistory 参数验证失败: 赛季格式错误', 'error');
            return ResponseAdapter::error('赛季格式错误');
        }

        try {
            $model = new EconomicsHistoryModel();
            $cache = new CacheManager();
            trace('EconomicsHistory::getSeasonHistory 模型创建成功，开始查询数据', 'info');

            // 临时绕过缓存以便调试
            $list = $model->getSeasonHistory(strtoupper($season));
            trace('EconomicsHistory::getSeasonHistory 查询结果: ' . json_encode($list, JSON_UNESCAPED_UNICODE), 'info');
            
            return ResponseAdapter::success('获取成功', $list);
        } catch (\Exception $e) {
            trace('EconomicsHistory::getSeasonHistory 异常: ' . $e->getMessage(), 'error');
            trace('EconomicsHistory::getSeasonHistory 异常堆栈: ' . $e->getTraceAsString(), 'error');
            // 错误脱敏
            return ResponseAdapter::error('获取失败');
        }
    }

    /**
     * 创建编年史事件
     */
    public function createEvent()
    {
        // 防爆破 + 认证（错误脱敏）
        if (!$this->verifyEditPassword()) {
            return ResponseAdapter::error('认证失败');
        }

        // 获取参数
        $data = $this->request->only([
            'season', 'event_date', 'event_time', 'title', 'description',
            'event_type_id', 'event_type_custom', 'event_type_color', 'importance_level', 'tags', 'card_position'
        ]);

        // 参数验证
        $validate = Validate::rule([
            'season'            => 'require|max:20',
            'event_date'        => 'require|date',
            'event_time'        => 'dateFormat:H:i:s',
            'title'             => 'require|max:200',
            'description'       => 'max:65535',
            'event_type_id'     => 'integer|gt:0',
            'event_type_custom' => 'max:100',
            'event_type_color'  => 'regex:/^#[0-9a-fA-F]{6}$/',
            'importance_level'  => 'between:1,5',
            'tags'              => 'array',
            'card_position'     => 'in:above,below'
        ]);

        if (!$validate->check($data)) {
            return ResponseAdapter::error($validate->getError());
        }

        // 验证事件类型：必须有event_type_id或event_type_custom
        if (empty($data['event_type_id']) && empty($data['event_type_custom'])) {
            return ResponseAdapter::error('必须指定事件类型ID或自定义事件类型');
        }

        // 设置默认值
        $data['importance_level'] = $data['importance_level'] ?? 3;
        $data['event_type_color'] = $data['event_type_color'] ?? '#1890ff';
        $data['card_position'] = $data['card_position'] ?? 'above';
        $data['status'] = 1;
        $data['sort_order'] = 0;

        // 处理标签，转换为JSON格式
        if (!empty($data['tags']) && is_array($data['tags'])) {
            $tags = array_filter($data['tags'], function($tag) {
                return !empty(trim($tag));
            });
            $data['tags'] = json_encode(array_values($tags), JSON_UNESCAPED_UNICODE);
        } else {
            $data['tags'] = json_encode([]);
        }

        try {
            $model = new EconomicsHistoryModel();
            $id = $model->createEvent($data);
            
            if ($id) {
                // 创建成功后清除所有相关缓存
                $this->clearAllEconomicsHistoryCache();
                
                trace('EconomicsHistory::createEvent 创建成功，ID: ' . $id, 'info');
                return ResponseAdapter::success('创建成功', ['id' => $id]);
            } else {
                return ResponseAdapter::error('创建失败');
            }
        } catch (\Exception $e) {
            trace('EconomicsHistory::createEvent 异常: ' . $e->getMessage(), 'error');
            // 错误脱敏
            return ResponseAdapter::error('创建失败');
        }
    }

    /**
     * 更新编年史事件
     */
    public function updateEvent()
    {
        // 防爆破 + 认证（错误脱敏）
        if (!$this->verifyEditPassword()) {
            return ResponseAdapter::error('认证失败');
        }

        // 获取ID
        $id = $this->request->param('id');
        if (empty($id)) {
            return ResponseAdapter::error('ID不能为空');
        }

        // 检查记录是否存在
        $model = new EconomicsHistoryModel();
        $record = $model->where('id', $id)->find();
        if (!$record) {
            return ResponseAdapter::error('记录不存在');
        }

        // 获取参数
        $data = $this->request->only([
            'event_date', 'event_time', 'title', 'description',
            'event_type_id', 'event_type_custom', 'event_type_color', 'importance_level', 'tags', 'card_position'
        ]);

        // 过滤空参数
        $data = array_filter($data, function($value) {
            return $value !== '' && $value !== null;
        });

        if (empty($data)) {
            return ResponseAdapter::error('没有要更新的数据');
        }

        // 参数验证
        $rules = [];
        if (isset($data['event_date'])) $rules['event_date'] = 'date';
        if (isset($data['event_time'])) $rules['event_time'] = 'dateFormat:H:i:s';
        if (isset($data['title'])) $rules['title'] = 'require|max:200';
        if (isset($data['description'])) $rules['description'] = 'max:65535';
        if (isset($data['event_type_id'])) $rules['event_type_id'] = 'integer|gt:0';
        if (isset($data['event_type_custom'])) $rules['event_type_custom'] = 'max:100';
        if (isset($data['event_type_color'])) $rules['event_type_color'] = 'regex:/^#[0-9a-fA-F]{6}$/';
        if (isset($data['importance_level'])) $rules['importance_level'] = 'between:1,5';
        if (isset($data['tags'])) $rules['tags'] = 'array';
        if (isset($data['card_position'])) $rules['card_position'] = 'in:above,below';

        if (!empty($rules)) {
            $validate = Validate::rule($rules);
            if (!$validate->check($data)) {
                return ResponseAdapter::error($validate->getError());
            }
        }

        // 处理标签，转换为JSON格式
        if (isset($data['tags']) && is_array($data['tags'])) {
            $tags = array_filter($data['tags'], function($tag) {
                return !empty(trim($tag));
            });
            $data['tags'] = json_encode(array_values($tags), JSON_UNESCAPED_UNICODE);
        }

        try {
            $result = $model->updateEvent($id, $data);
            
            if ($result) {
                // 更新成功后清除所有相关缓存
                $this->clearAllEconomicsHistoryCache();
                
                trace('EconomicsHistory::updateEvent 更新成功，ID: ' . $id, 'info');
                return ResponseAdapter::success('更新成功');
            } else {
                return ResponseAdapter::error('更新失败');
            }
        } catch (\Exception $e) {
            trace('EconomicsHistory::updateEvent 异常: ' . $e->getMessage(), 'error');
            // 错误脱敏
            return ResponseAdapter::error('更新失败');
        }
    }

    /**
     * 删除编年史事件
     */
    public function deleteEvent()
    {
        // 防爆破 + 认证（错误脱敏）
        if (!$this->verifyEditPassword()) {
            return ResponseAdapter::error('认证失败');
        }

        // 获取ID
        $id = $this->request->param('id');
        if (empty($id)) {
            return ResponseAdapter::error('ID不能为空');
        }

        // 检查记录是否存在
        $model = new EconomicsHistoryModel();
        $record = $model->where('id', $id)->find();
        if (!$record) {
            return ResponseAdapter::error('记录不存在');
        }

        try {
            $result = $model->deleteEvent($id);
            
            if ($result) {
                // 删除成功后清除所有相关缓存
                $this->clearAllEconomicsHistoryCache();
                
                trace('EconomicsHistory::deleteEvent 删除成功，ID: ' . $id, 'info');
                return ResponseAdapter::success('删除成功');
            } else {
                return ResponseAdapter::error('删除失败');
            }
        } catch (\Exception $e) {
            trace('EconomicsHistory::deleteEvent 异常: ' . $e->getMessage(), 'error');
            // 错误脱敏
            return ResponseAdapter::error('删除失败');
        }
    }

    /**
     * 获取所有赛季列表
     */
    public function getSeasonList()
    {
        try {
            $model = new EconomicsHistoryModel();
            $seasons = $model->field('season')
                ->where('status', 1)
                ->group('season')
                ->order('season desc')
                ->column('season');
            
            return ResponseAdapter::success('获取成功', $seasons);
        } catch (\Exception $e) {
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取事件类型列表
     */
    public function getEventTypeList()
    {
        try {
            $model = new EconomicsHistoryModel();
            $types = $model->getEventTypes();
            
            return ResponseAdapter::success('获取成功', $types);
        } catch (\Exception $e) {
            trace('EconomicsHistory::getEventTypeList 异常: ' . $e->getMessage(), 'error');
            return ResponseAdapter::error('获取失败');
        }
    }

    /**
     * 创建事件类型
     */
    public function createEventType()
    {
        // 防爆破 + 认证（错误脱敏）
        if (!$this->verifyEditPassword()) {
            return ResponseAdapter::error('认证失败');
        }

        // 获取参数
        $data = $this->request->only([
            'type_name', 'type_display_name', 'type_color', 'type_icon', 'sort_order'
        ]);

        // 参数验证
        $validate = Validate::rule([
            'type_name'         => 'require|max:50|unique:ba_economics_history_event_types',
            'type_display_name' => 'require|max:100',
            'type_color'        => 'regex:/^#[0-9a-fA-F]{6}$/',
            'type_icon'         => 'max:50',
            'sort_order'        => 'integer'
        ]);

        if (!$validate->check($data)) {
            return ResponseAdapter::error($validate->getError());
        }

        // 设置默认值
        $data['type_color'] = $data['type_color'] ?? '#1890ff';
        $data['type_icon'] = $data['type_icon'] ?? 'HistoryIcon';
        $data['sort_order'] = $data['sort_order'] ?? 0;
        $data['status'] = 1;

        try {
            $model = new EconomicsHistoryModel();
            $id = $model->createEventType($data);
            
            if ($id) {
                // 创建事件类型成功后清除所有相关缓存
                $this->clearAllEconomicsHistoryCache();
                
                trace('EconomicsHistory::createEventType 创建成功，ID: ' . $id, 'info');
                return ResponseAdapter::success('创建成功', ['id' => $id]);
            } else {
                return ResponseAdapter::error('创建失败');
            }
        } catch (\Exception $e) {
            trace('EconomicsHistory::createEventType 异常: ' . $e->getMessage(), 'error');
            return ResponseAdapter::error('创建失败');
        }
    }

    /**
     * 更新事件类型
     */
    public function updateEventType()
    {
        // 防爆破 + 认证（错误脱敏）
        if (!$this->verifyEditPassword()) {
            return ResponseAdapter::error('认证失败');
        }

        // 获取ID
        $id = $this->request->param('id');
        if (empty($id)) {
            return ResponseAdapter::error('ID不能为空');
        }

        // 获取参数
        $data = $this->request->only([
            'type_display_name', 'type_color', 'type_icon', 'sort_order', 'status'
        ]);

        // 过滤空参数
        $data = array_filter($data, function($value) {
            return $value !== '' && $value !== null;
        });

        if (empty($data)) {
            return ResponseAdapter::error('没有要更新的数据');
        }

        // 参数验证
        $rules = [];
        if (isset($data['type_display_name'])) $rules['type_display_name'] = 'require|max:100';
        if (isset($data['type_color'])) $rules['type_color'] = 'regex:/^#[0-9a-fA-F]{6}$/';
        if (isset($data['type_icon'])) $rules['type_icon'] = 'max:50';
        if (isset($data['sort_order'])) $rules['sort_order'] = 'integer';
        if (isset($data['status'])) $rules['status'] = 'in:0,1';

        if (!empty($rules)) {
            $validate = Validate::rule($rules);
            if (!$validate->check($data)) {
                return ResponseAdapter::error($validate->getError());
            }
        }

        try {
            $model = new EconomicsHistoryModel();
            $result = $model->updateEventType($id, $data);
            
            if ($result) {
                // 更新事件类型成功后清除所有相关缓存
                $this->clearAllEconomicsHistoryCache();
                
                trace('EconomicsHistory::updateEventType 更新成功，ID: ' . $id, 'info');
                return ResponseAdapter::success('更新成功');
            } else {
                return ResponseAdapter::error('更新失败');
            }
        } catch (\Exception $e) {
            trace('EconomicsHistory::updateEventType 异常: ' . $e->getMessage(), 'error');
            return ResponseAdapter::error('更新失败');
        }
    }

    /**
     * 删除事件类型
     */
    public function deleteEventType()
    {
        // 防爆破 + 认证（错误脱敏）
        if (!$this->verifyEditPassword()) {
            return ResponseAdapter::error('认证失败');
        }

        // 获取ID
        $id = $this->request->param('id');
        if (empty($id)) {
            return ResponseAdapter::error('ID不能为空');
        }

        try {
            $model = new EconomicsHistoryModel();
            
            // 检查是否有事件使用此类型
            $usageCount = $model->where('event_type_id', $id)->count();
            if ($usageCount > 0) {
                return ResponseAdapter::error('该事件类型正在被使用，无法删除');
            }
            
            $result = $model->deleteEventType($id);
            
            if ($result) {
                // 删除事件类型成功后清除所有相关缓存
                $this->clearAllEconomicsHistoryCache();
                
                trace('EconomicsHistory::deleteEventType 删除成功，ID: ' . $id, 'info');
                return ResponseAdapter::success('删除成功');
            } else {
                return ResponseAdapter::error('删除失败');
            }
        } catch (\Exception $e) {
            trace('EconomicsHistory::deleteEventType 异常: ' . $e->getMessage(), 'error');
            return ResponseAdapter::error('删除失败');
        }
    }

    /**
     * 验证编辑密码
     */
    public function verifyPassword()
    {
        // 防爆破 + 认证（错误脱敏）
        if (!$this->verifyEditPassword()) {
            return ResponseAdapter::error('密码验证失败');
        }

        return ResponseAdapter::success('密码验证成功');
    }
}