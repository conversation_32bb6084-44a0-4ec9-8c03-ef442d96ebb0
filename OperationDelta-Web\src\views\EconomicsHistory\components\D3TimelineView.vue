<template>
  <div class="d3-timeline">
    <!-- 控制区域 -->
    <div class="timeline-controls" :class="{ 'edit-mode': editMode }">
      <div class="controls-left">
        <h3 class="timeline-title">
          <n-icon :size="18">
            <HistoryIcon />
          </n-icon>
          编年史时间轴
          <n-badge :value="events.length" />
          <n-tag v-if="editMode" type="warning" size="small" style="margin-left: 8px">
            编辑模式
          </n-tag>
        </h3>
      </div>

      <div class="controls-right">
        <n-space>
          <n-button size="small" @click="zoomIn" :disabled="!canZoomIn">
            <template #icon>
              <n-icon>
                <AddOutline />
              </n-icon>
            </template>
            放大
          </n-button>
          <n-button size="small" @click="zoomOut" :disabled="!canZoomOut">
            <template #icon>
              <n-icon>
                <RemoveOutline />
              </n-icon>
            </template>
            缩小
          </n-button>
          <n-button size="small" @click="resetZoom">
            <template #icon>
              <n-icon>
                <RefreshOutline />
              </n-icon>
            </template>
            重置
          </n-button>
        </n-space>
      </div>
    </div>

    <!-- D3 SVG 容器 -->
    <div class="svg-container" ref="containerRef">
      <svg ref="svgRef" class="timeline-svg"></svg>
    </div>

    <!-- 事件详情弹窗 -->
    <n-modal 
      v-model:show="showDetailModal" 
      :mask-closable="true"
      preset="card"
      :style="{ width: '600px', maxWidth: '90vw' }"
      :title="selectedEvent?.title || '事件详情'"
      size="huge"
      :bordered="false"
    >
      <div class="event-detail" v-if="selectedEvent">
        <!-- 事件基本信息 -->
        <div class="detail-header">
          <div class="detail-meta">
            <n-tag 
              :type="getEventTypeTagType(selectedEvent)" 
              size="small"
            >
              <template #icon>
                <n-icon>
                  <HistoryIcon />
                </n-icon>
              </template>
              {{ getEventTypeDisplayName(selectedEvent) }}
            </n-tag>
            
            <n-divider vertical />
            
            <span class="detail-date">
              {{ formatDetailDate(selectedEvent.event_date) }}
            </span>
            
            <n-divider vertical />
            
            <div class="detail-importance">
              <span class="importance-label">重要程度:</span>
              <n-rate 
                :value="selectedEvent.importance_level" 
                readonly 
                size="small"
              />
            </div>
          </div>
        </div>

        <!-- 事件描述 -->
        <div class="detail-description" v-if="selectedEvent.description">
          <h4>详细描述</h4>
          <p>{{ selectedEvent.description }}</p>
        </div>

        <!-- 事件标签 -->
        <div class="detail-tags" v-if="selectedEvent.tags && selectedEvent.tags.length">
          <h4>相关标签</h4>
          <div class="tags-container">
            <n-tag 
              v-for="tag in selectedEvent.tags" 
              :key="tag" 
              type="default"
              round
            >
              {{ tag }}
            </n-tag>
          </div>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useMessage } from 'naive-ui'
import * as d3 from 'd3'
import { HistoryIcon } from '@/components/icons'
import { AddOutline, RemoveOutline, RefreshOutline } from '@vicons/ionicons5'
import type { EconomicsHistoryEvent } from '@/types/economicsHistory'

// Props
interface Props {
  events: EconomicsHistoryEvent[]
  editMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editMode: false
})

// Events
const emit = defineEmits<{
  edit: [event: EconomicsHistoryEvent]
  delete: [event: EconomicsHistoryEvent]
}>()

// Refs
const containerRef = ref<HTMLElement>()
const svgRef = ref<SVGElement>()
const showDetailModal = ref(false)
const selectedEvent = ref<EconomicsHistoryEvent | null>(null)

const message = useMessage()

// D3 相关变量
let svg: d3.Selection<SVGElement, unknown, null, undefined>
let g: d3.Selection<SVGGElement, unknown, null, undefined>
let xScale: d3.ScaleTime<number, number> | d3.ScaleOrdinal<Date, number>
let zoom: d3.ZoomBehavior<SVGElement, unknown>

// 缩放控制
const zoomLevel = ref(1)
const canZoomIn = ref(true)
const canZoomOut = ref(true)

// 常量配置
const MARGIN = { top: 60, right: 60, bottom: 80, left: 60 }
const TIMELINE_HEIGHT = 800
const NODE_RADIUS = 8
const CARD_WIDTH = 180
const CARD_HEIGHT = 70

// 横向时间轴布局常量
const BRANCH_Y_OFFSET = 80   // 卡片相对中轴的垂直偏移
const MIN_CARD_SPACING = 200 // 卡片之间的最小水平间距（增加到200px）
const COLLISION_DETECTION = true // 启用碰撞检测

// 事件聚合配置 - 优化参数
const EVENT_AGGREGATION_THRESHOLD = 6 // 超过这个数量的事件将被聚合显示
const MAX_VISIBLE_EVENTS_PER_DAY = 4  // 每天最多直接显示的事件数量
const DENSE_EVENT_THRESHOLD = 3       // 超过这个数量认为是密集事件，启用特殊布局

// 响应式布局参数，根据容器大小和事件密度动态调整
const getResponsiveConfig = (eventCount: number = 0) => {
  if (!containerRef.value) return {
    cardWidth: CARD_WIDTH,
    cardHeight: CARD_HEIGHT,
    yOffset: BRANCH_Y_OFFSET,
    minSpacing: MIN_CARD_SPACING,
    levelSpacing: 35,
    horizontalOffset: 25
  }
  
  const width = containerRef.value.clientWidth
  const eventDensity = eventCount / Math.max(width / 200, 1) // 事件密度：每200px宽度的事件数
  
  // 基础配置
  let config = {
    cardWidth: CARD_WIDTH,
    cardHeight: CARD_HEIGHT,
    yOffset: BRANCH_Y_OFFSET,
    minSpacing: MIN_CARD_SPACING,
    levelSpacing: 45, // 增加垂直层级间距
    horizontalOffset: 35 // 增加水平偏移
  }
  
  // 根据屏幕尺寸调整
  if (width < 480) {
    config = {
      cardWidth: 140,
      cardHeight: 60,
      yOffset: 60,
      minSpacing: 150, // 增加最小间距
      levelSpacing: 40, // 增加层级间距
      horizontalOffset: 30 // 增加水平偏移
    }
  } else if (width < 768) {
    config = {
      cardWidth: 160,
      cardHeight: 65,
      yOffset: 70,
      minSpacing: 170, // 增加最小间距
      levelSpacing: 42, // 增加层级间距
      horizontalOffset: 32 // 增加水平偏移
    }
  }
  
  // 根据事件密度进一步调整
  if (eventDensity > 2) {
    // 高密度时，减小卡片和间距
    config.cardWidth = Math.max(config.cardWidth * 0.85, 120)
    config.cardHeight = Math.max(config.cardHeight * 0.9, 50)
    config.levelSpacing = Math.max(config.levelSpacing * 0.8, 25)
    config.horizontalOffset = Math.max(config.horizontalOffset * 0.7, 15)
  } else if (eventDensity > 1.5) {
    // 中等密度时，适度调整
    config.cardWidth = Math.max(config.cardWidth * 0.92, 130)
    config.levelSpacing = Math.max(config.levelSpacing * 0.9, 28)
    config.horizontalOffset = Math.max(config.horizontalOffset * 0.85, 18)
  }
  
  return config
}

// 根据同一天事件数量获取自适应布局配置 - 默认优化版
const getAdaptiveLayoutConfig = (eventsCount: number, baseConfig: any) => {
  const config = { ...baseConfig }

  if (eventsCount <= 2) {
    // 少量事件：使用标准配置，保持较大间距
    config.levelSpacing = Math.max(config.levelSpacing * 1.1, 40) // 增加间距
    config.horizontalOffset = Math.max(config.horizontalOffset * 1.1, 30) // 增加水平偏移
    config.cardHeight = Math.max(config.cardHeight * 0.95, 60)
    return config
  } else if (eventsCount <= 4) {
    // 中等数量：保持较大间距
    config.levelSpacing = Math.max(config.levelSpacing * 1.0, 35) // 保持间距
    config.horizontalOffset = Math.max(config.horizontalOffset * 0.9, 25) // 稍微减少但保持较大值
    config.cardHeight = Math.max(config.cardHeight * 0.9, 55)
    config.cardWidth = Math.max(config.cardWidth * 0.95, 160)
  } else if (eventsCount <= 8) {
    // 较多事件：明显压缩
    config.levelSpacing = Math.max(config.levelSpacing * 0.7, 22)
    config.horizontalOffset = Math.max(config.horizontalOffset * 0.65, 15)
    config.cardHeight = Math.max(config.cardHeight * 0.85, 50)
    config.cardWidth = Math.max(config.cardWidth * 0.85, 140)
    config.yOffset = Math.max(config.yOffset * 0.95, 70)
  } else {
    // 大量事件：最大压缩
    config.levelSpacing = Math.max(config.levelSpacing * 0.6, 18)
    config.horizontalOffset = Math.max(config.horizontalOffset * 0.5, 12)
    config.cardHeight = Math.max(config.cardHeight * 0.75, 45)
    config.cardWidth = Math.max(config.cardWidth * 0.8, 120)
    config.yOffset = Math.max(config.yOffset * 0.85, 60)
  }

  return config
}

// 扁平化颜色配置
const importanceColors = {
  1: '#f5f5f5',
  2: '#d9f7be',
  3: '#bae7ff',
  4: '#ffe7ba',
  5: '#ffccc7'
}

const importanceBorderColors = {
  1: '#d9d9d9',
  2: '#52c41a',
  3: '#1890ff',
  4: '#fa8c16',
  5: '#f5222d'
}

// 按时间跨度选择合适的刻度间隔与格式
const getTickConfig = (start: Date, end: Date) => {
  const spanDays = (end.getTime() - start.getTime()) / 86400000
  if (spanDays > 365 * 4) {
    return { interval: d3.timeYear.every(1)!, format: d3.timeFormat('%Y') }
  }
  if (spanDays > 365 * 2) {
    return { interval: d3.timeMonth.every(3)!, format: d3.timeFormat('%Y-%m') }
  }
  if (spanDays > 365) {
    return { interval: d3.timeMonth.every(1)!, format: d3.timeFormat('%Y-%m') }
  }
  if (spanDays > 90) {
    return { interval: d3.timeWeek.every(2)!, format: d3.timeFormat('%m/%d') }
  }
  if (spanDays > 30) {
    return { interval: d3.timeWeek.every(1)!, format: d3.timeFormat('%m/%d') }
  }
  return { interval: d3.timeDay.every(3)!, format: d3.timeFormat('%m/%d') }
}

// 方法
const getEventTypeTagType = (event: EconomicsHistoryEvent) => {
  // 如果有事件类型对象，使用其显示名称判断
  const displayName = event.event_type?.type_display_name || event.event_type_custom || '未知'
  
  const typeMap: Record<string, string> = {
    '价格调整': 'warning',
    '新物品上线': 'success',
    '游戏更新': 'info',
    '平衡性调整': 'default',
    '重大事件': 'error',
    'Bug修复': 'warning',
    '更新': 'info',
    '市场变动': 'warning',
    '政策调整': 'default',
    '赛季开始': 'success',
    '表事件类型': 'info'
  }
  return typeMap[displayName] || 'default'
}

const formatDate = (dateStr: string) => {
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      return '无效日期'
    }
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  } catch (error) {
    console.warn('日期格式化失败:', dateStr, error)
    return '日期错误'
  }
}

const formatDetailDate = (dateStr: string) => {
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      return '无效日期'
    }
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  } catch (error) {
    console.warn('详细日期格式化失败:', dateStr, error)
    return '日期错误'
  }
}

// 事件类型显示名称获取
const getEventTypeDisplayName = (event: EconomicsHistoryEvent) => {
  return event.event_type?.type_display_name || event.event_type_custom || '未知类型'
}

// 获取事件类型颜色
const getEventTypeColor = (event: EconomicsHistoryEvent) => {
  return event.event_type?.type_color || event.event_type_color || '#1890ff'
}

// 将事件按实际日期和时间分组 - 支持同一天多个时间点
const groupEventsToTimePoints = (events: EconomicsHistoryEvent[]) => {
  if (events.length === 0) return new Map()

  console.log('🔄 开始分组事件...')
  // 使用字符串作为key来避免Date对象重复问题
  const groupedByString = new Map<string, EconomicsHistoryEvent[]>()
  let processedCount = 0
  let skippedCount = 0

  // 按实际事件日期和时间分组
  events.forEach((event, index) => {
    console.log(`处理事件 ${index + 1}/${events.length}:`, event.title, event.event_date, event.event_time)

    // 验证日期字符串是否有效
    if (!event.event_date) {
      console.warn('⚠️ 事件缺少日期信息:', event)
      skippedCount++
      return
    }

    const eventDate = new Date(event.event_date)

    // 检查日期是否有效
    if (isNaN(eventDate.getTime())) {
      console.warn('⚠️ 无效的日期格式:', event.event_date, event)
      skippedCount++
      return
    }

    // 生成标准化的日期字符串作为key (YYYY-MM-DD格式)
    const year = eventDate.getFullYear()
    const month = String(eventDate.getMonth() + 1).padStart(2, '0')
    const day = String(eventDate.getDate()).padStart(2, '0')
    let timePointKey = `${year}-${month}-${day}`

    // 如果有具体时间，添加到键中以支持同一天多个时间点
    if (event.event_time && event.event_time.trim()) {
      timePointKey += `_${event.event_time.trim()}`
    }

    console.log(`  ➡️ 分组到时间点键: ${timePointKey}`)

    if (!groupedByString.has(timePointKey)) {
      groupedByString.set(timePointKey, [])
    }

    groupedByString.get(timePointKey)!.push(event)
    processedCount++
  })
  
  // 将字符串key转换回Date对象，支持时间信息
  const groupedEvents = new Map<Date, EconomicsHistoryEvent[]>()
  groupedByString.forEach((events, timePointString) => {
    // 解析时间点字符串：YYYY-MM-DD 或 YYYY-MM-DD_HH:MM
    const [datePart, timePart] = timePointString.split('_')
    const [year, month, day] = datePart.split('-').map(Number)

    let dateKey: Date
    if (timePart) {
      // 有具体时间，创建包含时间的Date对象
      const [hour, minute] = timePart.split(':').map(Number)
      dateKey = new Date(year, month - 1, day, hour || 0, minute || 0)
    } else {
      // 没有具体时间，使用日期
      dateKey = new Date(year, month - 1, day)
    }

    groupedEvents.set(dateKey, events)
  })
  
  console.log('📈 分组结果:')
  console.log(`  - 成功处理: ${processedCount} 个事件`)
  console.log(`  - 跳过: ${skippedCount} 个事件`)
  console.log(`  - 总共分组: ${groupedEvents.size} 个时间点`)
  
  // 显示每个分组的详细信息
  const sortedGroups = Array.from(groupedEvents.entries())
    .sort(([a], [b]) => a.getTime() - b.getTime())
  
  console.log('🔍 详细分组检查:')
  sortedGroups.forEach(([date, events], index) => {
    console.log(`  📅 组 ${index + 1}: ${date.toLocaleDateString()} (${date.getTime()}) - ${events.length} 个事件`, events.map(e => e.title))
  })
  
  return groupedEvents
}

// 处理事件聚合：当同一天事件过多时进行智能聚合
const processEventAggregation = (groupedEvents: Map<Date, EconomicsHistoryEvent[]>) => {
  const processedEvents = new Map<Date, Array<{
    type: 'single' | 'group'
    events: EconomicsHistoryEvent[]
    displayTitle?: string
    isExpanded?: boolean
  }>>()

  groupedEvents.forEach((events, date) => {
    if (events.length <= EVENT_AGGREGATION_THRESHOLD) {
      // 事件数量不多，直接显示所有事件
      processedEvents.set(date, events.map(event => ({
        type: 'single',
        events: [event]
      })))
    } else {
      // 事件过多，进行聚合处理
      const processedList = []

      // 按重要程度排序，优先显示重要事件
      const sortedEvents = [...events].sort((a, b) =>
        (b.importance_level || 0) - (a.importance_level || 0)
      )

      // 显示前几个重要事件
      for (let i = 0; i < Math.min(MAX_VISIBLE_EVENTS_PER_DAY, sortedEvents.length); i++) {
        processedList.push({
          type: 'single' as const,
          events: [sortedEvents[i]]
        })
      }

      // 剩余事件聚合成一个组
      if (sortedEvents.length > MAX_VISIBLE_EVENTS_PER_DAY) {
        const remainingEvents = sortedEvents.slice(MAX_VISIBLE_EVENTS_PER_DAY)
        processedList.push({
          type: 'group' as const,
          events: remainingEvents,
          displayTitle: `+${remainingEvents.length}个事件`,
          isExpanded: false
        })
      }

      processedEvents.set(date, processedList)
    }
  })

  return processedEvents
}

// 改进的卡片布局算法，支持智能碰撞检测和空间优化
// 注意：这个函数现在需要在时间轴尺度确定后调用
const calculateEventLayoutsWithScale = (groupedEvents: Map<Date, EconomicsHistoryEvent[]>, timeScale: any) => {
  // 计算总事件数以供自适应配置使用
  const totalEventCount = Array.from(groupedEvents.values()).reduce((sum, events) => sum + events.length, 0)
  const config = getResponsiveConfig(totalEventCount)
  
  const layouts = new Map<Date, Array<{
    event: EconomicsHistoryEvent
    side: number
    yOffset: number
    xOffset: number
    availableWidth: number
    absoluteX: number  // 添加绝对坐标
  }>>()
  
  // 存储全局已占用的位置（使用绝对坐标），用于碰撞检测
  const occupiedPositions: Array<{
    absoluteX: number  // 时间轴上的绝对X坐标
    absoluteY: number  // 相对于时间轴中心的Y坐标
    width: number
    height: number
    timePoint: Date
  }> = []
  
  let maxYOffset = 0 // 记录最大的Y偏移量，用于调整SVG高度
  
  // 按时间顺序处理每个时间点
  const sortedTimePoints = Array.from(groupedEvents.keys()).sort((a, b) => a.getTime() - b.getTime())
  
  sortedTimePoints.forEach((timePoint) => {
    const events = groupedEvents.get(timePoint)!
    
    // 计算时间点在时间轴上的绝对X坐标
    let timePointX: number
    if (typeof timeScale === 'function' && 'bandwidth' in timeScale) {
      timePointX = (timeScale as any)(timePoint)
      if (timePointX === undefined) {
        const domain = (timeScale as any).domain() as Date[]
        const closestPoint = domain.reduce((prev, curr) => 
          Math.abs(curr.getTime() - timePoint.getTime()) < Math.abs(prev.getTime() - timePoint.getTime()) ? curr : prev
        )
        timePointX = (timeScale as any)(closestPoint) || 0
      }
    } else {
      timePointX = (timeScale as d3.ScaleTime<number, number>)(timePoint)
    }
    
    // 如果无法获取有效坐标，跳过这个时间点
    if (isNaN(timePointX) || timePointX === undefined) {
      console.warn('无法获取时间点坐标:', timePoint, timePointX)
      return
    }
    
    const eventLayouts: Array<{
      event: EconomicsHistoryEvent
      side: number
      yOffset: number
      xOffset: number
      availableWidth: number
      absoluteX: number
    }> = []
    
    events.forEach((event, eventIndex) => {
      // 尝试找到不重叠的位置
      const position = findNonOverlappingPositionWithCoords(
        timePoint,
        timePointX,
        eventIndex,
        events.length,
        occupiedPositions,
        config,
        event
      )
      
      // 更新最大偏移量
      maxYOffset = Math.max(maxYOffset, Math.abs(position.yOffset) + config.cardHeight / 2)
      
      // 计算卡片的绝对位置
      const cardAbsoluteX = timePointX + position.xOffset
      
      // 记录占用的空间（使用绝对坐标）
      occupiedPositions.push({
        absoluteX: cardAbsoluteX,
        absoluteY: position.yOffset,
        width: config.cardWidth,
        height: config.cardHeight,
        timePoint
      })
      
      eventLayouts.push({
        event,
        side: position.side,
        yOffset: position.yOffset,
        xOffset: position.xOffset,
        availableWidth: config.cardWidth,
        absoluteX: cardAbsoluteX
      })
    })
    
    layouts.set(timePoint, eventLayouts)
  })
  
  // 存储最大偏移量供SVG高度计算使用
  ;(layouts as any).maxYOffset = maxYOffset
  
  return layouts
}

// 兼容性包装器：没有scale时使用简化版本
const calculateEventLayouts = (groupedEvents: Map<Date, EconomicsHistoryEvent[]>) => {
  const totalEventCount = Array.from(groupedEvents.values()).reduce((sum, events) => sum + events.length, 0)
  const config = getResponsiveConfig(totalEventCount)
  
  const layouts = new Map<Date, Array<{
    event: EconomicsHistoryEvent
    side: number
    yOffset: number
    xOffset: number
    availableWidth: number
  }>>()
  
  let maxYOffset = 0
  let globalIndex = 0
  
  const sortedTimePoints = Array.from(groupedEvents.keys()).sort((a, b) => a.getTime() - b.getTime())
  
  sortedTimePoints.forEach((timePoint) => {
    const events = groupedEvents.get(timePoint)!
    const eventLayouts: Array<{
      event: EconomicsHistoryEvent
      side: number
      yOffset: number
      xOffset: number
      availableWidth: number
    }> = []
    
    events.forEach((event, eventIndex) => {
      // 简化的布局算法
      const side = globalIndex % 2 === 0 ? -1 : 1
      const level = Math.floor(globalIndex / 2)
      const yOffset = side * (config.yOffset + level * config.levelSpacing)
      const xOffset = (eventIndex > 0 ? eventIndex * config.horizontalOffset * side : 0)
      
      maxYOffset = Math.max(maxYOffset, Math.abs(yOffset) + config.cardHeight / 2)
      globalIndex++
      
      eventLayouts.push({
        event,
        side,
        yOffset,
        xOffset,
        availableWidth: config.cardWidth
      })
    })
    
    layouts.set(timePoint, eventLayouts)
  })
  
  ;(layouts as any).maxYOffset = maxYOffset
  return layouts
}

// 基于绝对坐标的智能位置查找算法 - 支持单个事件位置设置
const findNonOverlappingPositionWithCoords = (
  timePoint: Date,
  timePointX: number,
  eventIndex: number,
  totalEventsAtTimePoint: number,
  occupiedPositions: Array<{
    absoluteX: number
    absoluteY: number
    width: number
    height: number
    timePoint: Date
  }>,
  config: any,
  event: EconomicsHistoryEvent // 添加事件参数以获取其位置设置
) => {
  const baseOffset = config.yOffset || 100 // 增加基础偏移量
  const levelSpacing = config.levelSpacing || 60 // 增加层级间距
  const horizontalOffset = config.horizontalOffset || 50 // 增加水平偏移

  // 根据同一天事件数量动态调整布局策略
  const adaptiveConfig = getAdaptiveLayoutConfig(totalEventsAtTimePoint, config)

  // 根据事件的card_position设置决定卡片位置
  let sideOptions: number[] = []
  const eventCardPosition = event.card_position || 'auto' // 默认自动

  switch(eventCardPosition) {
    case 'above':
      sideOptions = [-1] // 只在上方
      break
    case 'below':
      sideOptions = [1] // 只在下方
      break
    case 'auto':
    default:
      sideOptions = [-1, 1] // 自动选择（上方优先）
      break
  }

  // 生成候选位置 - 优化版
  const candidates = []

  // 策略1: 同一天事件垂直堆叠布局 - 针对同一天多事件优化，增大间距避免遮挡
  if (totalEventsAtTimePoint > 1) {
    // 根据事件数量动态调整垂直间距，确保不重叠
    // 事件越多，间距应该越大，确保绝对不重叠
    let verticalSpacing = Math.max(adaptiveConfig.levelSpacing, 60) // 最小60px间距

    // 根据事件数量增加间距，而不是减少
    if (totalEventsAtTimePoint > 6) {
      verticalSpacing = Math.max(80, verticalSpacing * 1.3) // 大量事件时增加间距
    } else if (totalEventsAtTimePoint > 4) {
      verticalSpacing = Math.max(70, verticalSpacing * 1.2) // 中等事件时增加间距
    } else if (totalEventsAtTimePoint > 2) {
      verticalSpacing = Math.max(65, verticalSpacing * 1.1) // 少量事件时稍微增加间距
    }
    
    // 确保有足够的层级来容纳所有事件
    const maxLevelsPerSide = Math.max(Math.ceil(totalEventsAtTimePoint / 2), totalEventsAtTimePoint)

    for (let level = 0; level < maxLevelsPerSide; level++) {
      for (const side of sideOptions) {
        const yOffset = side * (baseOffset + level * verticalSpacing)

        // 同一天事件优先使用零水平偏移，确保垂直对齐
        candidates.push({
          side,
          yOffset,
          xOffset: 0, // 优先零偏移
          priority: level * 0.2, // 进一步降低层级优先级权重
          layoutType: 'vertical'
        })

        // 如果零偏移位置被占用，生成更多水平偏移选项，使用更大的间距
        for (let hOffset = 1; hOffset <= 8; hOffset++) {
          const xOffset = hOffset * (adaptiveConfig.horizontalOffset * 2.0) * (side > 0 ? 1 : -1) // 进一步增加水平间距
          candidates.push({
            side,
            yOffset,
            xOffset,
            priority: level * 0.2 + hOffset * 0.5, // 进一步降低水平偏移惩罚，鼓励使用水平分离
            layoutType: 'vertical'
          })
        }
      }
    }
  }

  // 策略2: 紧凑型布局 - 当事件数量较多时补充使用，进一步增大间距
  if (totalEventsAtTimePoint > DENSE_EVENT_THRESHOLD) {
    // 使用更保守的分层策略，确保足够间距
    const compactLevelSpacing = Math.max(50, adaptiveConfig.levelSpacing * 1.1) // 最小50px，增加间距
    const compactHorizontalOffset = Math.max(40, adaptiveConfig.horizontalOffset * 1.2) // 最小40px，增加水平间距

    // 生成紧凑型候选位置，但保证间距足够
    const maxLevels = Math.max(8, Math.ceil(totalEventsAtTimePoint / 1.2))
    const maxHorizontalOffsets = Math.min(5, Math.ceil(totalEventsAtTimePoint / 2.5))

    for (let level = 0; level < maxLevels; level++) {
      // 只使用允许的方向
      for (const side of sideOptions) {
        const yOffset = side * (baseOffset + level * compactLevelSpacing)

        // 优先垂直布局，减少水平偏移
        for (let hOffset = 0; hOffset <= maxHorizontalOffsets; hOffset++) {
          const xOffset = hOffset * compactHorizontalOffset * (side > 0 ? 1 : -1)

          candidates.push({
            side,
            yOffset,
            xOffset,
            priority: level * 0.6 + Math.abs(xOffset) * 0.15, // 增加水平偏移惩罚
            layoutType: 'compact'
          })
        }
      }
    }
  }

  // 策略3: 标准布局 - 适用于中等数量的事件
  for (let level = 0; level < Math.max(5, Math.ceil(totalEventsAtTimePoint / 2) + 2); level++) {
    // 只使用允许的方向
    for (const side of sideOptions) {
      const yOffset = side * (baseOffset + level * levelSpacing)

      // 增加水平位置的生成，使用更大的水平间距
      for (let hOffset = 0; hOffset <= 5; hOffset++) {
        const xOffset = hOffset * horizontalOffset * 1.5 * (side > 0 ? 1 : -1) // 增加水平间距

        candidates.push({
          side,
          yOffset,
          xOffset,
          priority: level + Math.abs(xOffset) * 0.1, // 降低水平偏移惩罚，鼓励水平分离
          layoutType: 'standard'
        })
      }
    }
  }

  // 策略4: 扇形布局 - 当事件非常多时作为最后手段
  if (totalEventsAtTimePoint > 8) {
    const fanRadius = baseOffset * 1.1
    const angleStep = Math.PI / Math.max(totalEventsAtTimePoint, 6)

    for (let i = 0; i < totalEventsAtTimePoint; i++) {
      const angle = -Math.PI/2 + (i - totalEventsAtTimePoint/2 + 0.5) * angleStep
      const fanXOffset = fanRadius * Math.sin(angle) * 0.4 // 减少水平扩散
      const fanYOffset = fanRadius * Math.cos(angle)
      const calculatedSide = fanYOffset > 0 ? 1 : -1

      // 只有当计算出的方向在允许范围内时才添加候选位置
      if (sideOptions.includes(calculatedSide)) {
        candidates.push({
          side: calculatedSide,
          yOffset: fanYOffset,
          xOffset: fanXOffset,
          priority: Math.abs(angle) + 3, // 扇形布局优先级最低
          layoutType: 'fan'
        })
      }
    }
  }

  // 按优先级排序，垂直布局优先
  candidates.sort((a, b) => {
    // 垂直布局优先级最高
    if (a.layoutType === 'vertical' && b.layoutType !== 'vertical') return -1
    if (b.layoutType === 'vertical' && a.layoutType !== 'vertical') return 1
    // 其次是紧凑布局
    if (a.layoutType === 'compact' && b.layoutType !== 'compact') return -1
    if (b.layoutType === 'compact' && a.layoutType !== 'compact') return 1
    return a.priority - b.priority
  })

  // 查找第一个不重叠的位置
  for (const candidate of candidates) {
    if (!isPositionOccupiedAbsolute(candidate, timePointX, occupiedPositions, config)) {
      console.log(`✅ 找到可用位置: side=${candidate.side}, yOffset=${candidate.yOffset}, xOffset=${candidate.xOffset}, layoutType=${candidate.layoutType}`)
      return candidate
    } else {
      console.log(`❌ 位置被占用: side=${candidate.side}, yOffset=${candidate.yOffset}, xOffset=${candidate.xOffset}`)
    }
  }

  // 如果仍然找不到，使用强制分层策略
  console.warn(`⚠️ 所有候选位置都被占用，使用强制分层策略 - 事件索引: ${eventIndex}, 总事件数: ${totalEventsAtTimePoint}`)

  let fallbackSide = eventIndex % 2 === 0 ? -1 : 1

  // 如果计算出的方向不在允许范围内，使用第一个允许的方向
  if (!sideOptions.includes(fallbackSide)) {
    fallbackSide = sideOptions[0] || -1 // 默认上方
  }

  // 使用更激进的分层策略，确保绝对不重叠
  const fallbackLevel = eventIndex + Math.floor(totalEventsAtTimePoint / 2) // 每个事件都有独立的层级
  const fallbackXOffset = (eventIndex % 8) * (horizontalOffset * 2.0) * fallbackSide // 进一步增加水平间距

  const fallbackPosition = {
    side: fallbackSide,
    yOffset: fallbackSide * (baseOffset + fallbackLevel * (levelSpacing * 1.5)), // 进一步增加垂直间距
    xOffset: fallbackXOffset,
    layoutType: 'fallback'
  }

  console.log(`🔧 强制分层结果: side=${fallbackPosition.side}, yOffset=${fallbackPosition.yOffset}, xOffset=${fallbackPosition.xOffset}`)

  return fallbackPosition
}

// 基于绝对坐标的碰撞检测 - 优化版，增强防重叠能力
const isPositionOccupiedAbsolute = (
  candidate: { yOffset: number; xOffset: number },
  timePointX: number,
  occupiedPositions: Array<{
    absoluteX: number
    absoluteY: number
    width: number
    height: number
    timePoint: Date
  }>,
  config: any
) => {
  // 更保守的安全间距策略，确保绝对不重叠
  const totalOccupied = occupiedPositions.length
  let padding = 25 // 基础安全间距大幅提高到25px

  // 根据事件密度调整间距，但保持较大的最小值
  if (totalOccupied > 20) {
    padding = 20  // 超密集事件时最小20px
  } else if (totalOccupied > 15) {
    padding = 22  // 大量事件时使用22px
  } else if (totalOccupied > 10) {
    padding = 25  // 中等数量时使用25px
  } else if (totalOccupied > 5) {
    padding = 28  // 少量事件时使用28px
  } else {
    padding = 35  // 极少事件时使用更大间距
  }

  const cardWidth = config.cardWidth
  const cardHeight = config.cardHeight

  // 计算候选位置的绝对坐标
  const candidateAbsoluteX = timePointX + candidate.xOffset
  const candidateAbsoluteY = candidate.yOffset

  // 候选位置的边界框（绝对坐标），增加额外的垂直间距
  const verticalPadding = Math.max(padding, cardHeight * 0.25) // 垂直间距至少是卡片高度的25%
  const horizontalPadding = Math.max(padding, cardWidth * 0.15) // 水平间距至少是卡片宽度的15%
  
  const candidateBox = {
    left: candidateAbsoluteX - cardWidth / 2 - horizontalPadding,
    right: candidateAbsoluteX + cardWidth / 2 + horizontalPadding,
    top: candidateAbsoluteY - cardHeight / 2 - verticalPadding,
    bottom: candidateAbsoluteY + cardHeight / 2 + verticalPadding
  }

  // 检查与已有位置的重叠，使用更严格的碰撞检测
  for (const occupied of occupiedPositions) {
    // 计算时间距离和空间距离，使用更严格的检测
    const timeDiff = Math.abs(candidateAbsoluteX - occupied.absoluteX)
    const spaceDiff = Math.abs(candidateAbsoluteY - occupied.absoluteY)
    
    // 基于时间和空间距离动态调整间距
    let timeBasedHorizontalPadding = horizontalPadding
    let spaceBasedVerticalPadding = verticalPadding
    
    // 同一时间点或非常接近的时间点使用更大的间距
    if (timeDiff < 50) {
      timeBasedHorizontalPadding = horizontalPadding * 3.0 // 同一时间点三倍间距
    } else if (timeDiff < 100) {
      timeBasedHorizontalPadding = horizontalPadding * 2.0
    } else if (timeDiff < 200) {
      timeBasedHorizontalPadding = horizontalPadding * 1.5
    }

    // 垂直距离很近时也要加大间距
    if (spaceDiff < cardHeight * 2.0) {
      spaceBasedVerticalPadding = verticalPadding * 2.5 // 增加垂直间距
    } else if (spaceDiff < cardHeight * 3.0) {
      spaceBasedVerticalPadding = verticalPadding * 1.8
    }

    const occupiedBox = {
      left: occupied.absoluteX - occupied.width / 2 - timeBasedHorizontalPadding,
      right: occupied.absoluteX + occupied.width / 2 + timeBasedHorizontalPadding,
      top: occupied.absoluteY - occupied.height / 2 - spaceBasedVerticalPadding,
      bottom: occupied.absoluteY + occupied.height / 2 + spaceBasedVerticalPadding
    }

    // 使用更严格的重叠检测
    const hasHorizontalOverlap = candidateBox.left < occupiedBox.right && candidateBox.right > occupiedBox.left
    const hasVerticalOverlap = candidateBox.top < occupiedBox.bottom && candidateBox.bottom > occupiedBox.top
    
    if (hasHorizontalOverlap && hasVerticalOverlap) {
      // 额外检查：如果重叠面积很大，则认为冲突更严重
      const overlapWidth = Math.min(candidateBox.right, occupiedBox.right) - Math.max(candidateBox.left, occupiedBox.left)
      const overlapHeight = Math.min(candidateBox.bottom, occupiedBox.bottom) - Math.max(candidateBox.top, occupiedBox.top)
      const overlapArea = overlapWidth * overlapHeight
      const candidateArea = (candidateBox.right - candidateBox.left) * (candidateBox.bottom - candidateBox.top)

      console.log(`🔍 检测到重叠: 重叠面积=${overlapArea.toFixed(1)}, 候选区域=${candidateArea.toFixed(1)}, 比例=${(overlapArea/candidateArea*100).toFixed(1)}%`)

      // 如果有任何重叠，都认为冲突（最严格的检测）
      if (overlapArea > 0) {
        return true // 位置被占用
      }
    }
  }

  return false // 位置可用
}

// 旧版智能位置查找算法：兼容性保留
const findNonOverlappingPosition = (
  timePoint: Date,
  eventIndex: number,
  totalEventsAtTimePoint: number,
  occupiedPositions: Array<{
    x: number
    y: number
    width: number
    height: number
    timePoint: Date
  }>,
  config: any
) => {
  const baseOffset = config.yOffset || 80
  const levelSpacing = config.levelSpacing || 35
  const horizontalOffset = config.horizontalOffset || 25
  
  const side = eventIndex % 2 === 0 ? -1 : 1
  const level = Math.floor(eventIndex / 2)
  const yOffset = side * (baseOffset + level * levelSpacing)
  const xOffset = (eventIndex > 0 ? (eventIndex % 3) * horizontalOffset * side : 0)
  
  return {
    side,
    yOffset,
    xOffset
  }
}

// 检查位置是否被占用（碰撞检测）
const isPositionOccupied = (
  candidate: { yOffset: number; xOffset: number },
  occupiedPositions: Array<{
    x: number
    y: number
    width: number
    height: number
    timePoint: Date
  }>,
  config: any,
  currentTimePoint: Date
) => {
  const padding = 10 // 卡片之间的最小间距
  const cardWidth = config.cardWidth
  const cardHeight = config.cardHeight
  
  // 候选位置的边界框
  const candidateBox = {
    left: candidate.xOffset - cardWidth / 2 - padding,
    right: candidate.xOffset + cardWidth / 2 + padding,
    top: candidate.yOffset - cardHeight / 2 - padding,
    bottom: candidate.yOffset + cardHeight / 2 + padding
  }
  
  // 检查与已有位置的重叠（只检查相近时间点的卡片）
  for (const occupied of occupiedPositions) {
    // 只检查相近时间点的卡片（避免距离很远的卡片影响布局）
    const timeDiff = Math.abs(currentTimePoint.getTime() - occupied.timePoint.getTime())
    const maxTimeDiff = 7 * 24 * 60 * 60 * 1000 // 7天
    
    if (timeDiff > maxTimeDiff) continue
    
    const occupiedBox = {
      left: occupied.x - occupied.width / 2,
      right: occupied.x + occupied.width / 2,
      top: occupied.y - occupied.height / 2,
      bottom: occupied.y + occupied.height / 2
    }
    
    // 检查是否重叠
    if (
      candidateBox.left < occupiedBox.right &&
      candidateBox.right > occupiedBox.left &&
      candidateBox.top < occupiedBox.bottom &&
      candidateBox.bottom > occupiedBox.top
    ) {
      return true // 位置被占用
    }
  }
  
  return false // 位置可用
}

// 计算卡片位置以避免重叠（保留用于兼容性）
const calculateCardPositions = (events: EconomicsHistoryEvent[]) => {
  if (!containerRef.value) return []
  
  const config = getResponsiveConfig()
  const positions: Array<{
    side: number
    availableWidth: number
    xOffset: number
  }> = []
  
  const containerWidth = containerRef.value.clientWidth - MARGIN.left - MARGIN.right
  
  events.forEach((event, index) => {
    const eventX = xScale(new Date(event.event_date))
    
    // 检查与前面事件的距离
    let side = index % 2 === 0 ? -1 : 1 // 默认上下交替
    let availableWidth = config.cardWidth
    
    // 检查是否需要调整位置避免重叠
    if (COLLISION_DETECTION && index > 0) {
      for (let i = index - 1; i >= Math.max(0, index - 3); i--) {
        const prevEventX = xScale(new Date(events[i].event_date))
        const distance = Math.abs(eventX - prevEventX)
        
        if (distance < config.minSpacing && positions[i].side === side) {
          // 如果太近且在同一侧，切换到另一侧
          side = -side
          break
        }
      }
      
      // 根据可用空间调整卡片宽度
      const leftSpace = eventX
      const rightSpace = containerWidth - eventX
      const minWidth = Math.min(100, config.cardWidth * 0.7)
      availableWidth = Math.min(config.cardWidth, Math.min(leftSpace, rightSpace) * 1.6)
      availableWidth = Math.max(availableWidth, minWidth)
    }
    
    positions.push({
      side,
      availableWidth,
      xOffset: 0
    })
  })
  
  return positions
}

const initD3Timeline = () => {
  if (!containerRef.value || !svgRef.value || props.events.length === 0) return

  try {
    const container = containerRef.value
    const width = container.clientWidth

    // 验证数据 - 添加调试信息
    console.log('📊 时间轴数据分析:')
    console.log('原始事件数量:', props.events.length)
    console.log('原始事件数据:', props.events)
    
    const validEvents = props.events.filter(event => {
      const hasDate = !!event.event_date
      const isValidDate = hasDate && !isNaN(new Date(event.event_date).getTime())
      
      if (!hasDate) {
        console.warn('⚠️ 事件缺少日期:', event)
      }
      if (hasDate && !isValidDate) {
        console.warn('⚠️ 事件日期无效:', event.event_date, event)
      }
      
      return hasDate && isValidDate
    })

    console.log('✅ 有效事件数量:', validEvents.length)
    console.log('✅ 有效事件数据:', validEvents)

    if (validEvents.length === 0) {
      console.error('❌ 没有有效的事件数据')
      return
    }

    // 预计算事件布局以确定所需的SVG高度
    const layoutGroupedEvents = groupEventsToTimePoints(validEvents)
    const eventLayouts = calculateEventLayouts(layoutGroupedEvents)
    const maxYOffset = (eventLayouts as any).maxYOffset || 200
    
    // 动态计算SVG高度，确保所有卡片都可见
    const minHeight = TIMELINE_HEIGHT
    const dynamicHeight = Math.max(minHeight, MARGIN.top + MARGIN.bottom + maxYOffset * 2 + 100)
    const height = dynamicHeight

    // 清空之前的内容
    d3.select(svgRef.value).selectAll("*").remove()

    // 设置SVG
    svg = d3.select(svgRef.value)
      .attr('width', width)
      .attr('height', height)

    // 主要绘图区域
    g = svg.append('g')
      .attr('transform', `translate(${MARGIN.left},${MARGIN.top})`)

    // 智能时间尺度：对于时间跨度大的数据，使用非线性分布
    const timeExtent = d3.extent(validEvents, d => new Date(d.event_date)) as [Date, Date]
    if (!timeExtent[0] || !timeExtent[1]) return

    // 计算时间跨度
    const startDate = new Date(timeExtent[0])
    const endDate = new Date(timeExtent[1])
    const timeSpan = endDate.getTime() - startDate.getTime()
    const timeSpanYears = timeSpan / (365 * 24 * 60 * 60 * 1000)

    // 智能时间轴布局策略：解决时间跨度大和事件分布不均的问题
    const timeGroupedEvents = groupEventsToTimePoints(validEvents)
    const timePoints = Array.from(timeGroupedEvents.keys()).sort((a, b) => a.getTime() - b.getTime())
    const availableWidth = width - MARGIN.left - MARGIN.right
    const pointCount = timePoints.length
    
    // 计算事件重要性权重，用于布局决策
    const calculateEventWeight = (events: EconomicsHistoryEvent[]) => {
      if (!events || events.length === 0) return 1
      
      let weight = 1
      // 事件数量权重
      weight += events.length * 0.5
      // 重要程度权重
      const avgImportance = events.reduce((sum, e) => sum + (e.importance_level || 1), 0) / events.length
      weight += avgImportance * 0.8
      // 高重要度事件加权
      const highImportanceCount = events.filter(e => (e.importance_level || 0) >= 4).length
      weight += highImportanceCount * 1.2
      
      return Math.min(weight, 5) // 限制最大权重
    }
    
    // 为每个时间点计算权重
    const timePointWeights = new Map<Date, number>()
    timePoints.forEach(date => {
      const events = timeGroupedEvents.get(date) || []
      timePointWeights.set(date, calculateEventWeight(events))
    })
    
    // 根据时间跨度和事件密度选择布局策略
    if (timeSpanYears > 1 || pointCount > 8) {
      // 长时间跨度：使用权重分配布局
      console.log(`时间跨度: ${timeSpanYears.toFixed(1)}年, 时间点数量: ${pointCount}`)
      
      const totalWeight = Array.from(timePointWeights.values()).reduce((sum, weight) => sum + weight, 0)
      const minSpacing = Math.max(100, availableWidth * 0.06) // 最小间距
      
      // 检查是否需要使用权重分配
      const averageSpacing = availableWidth / pointCount
      if (averageSpacing < minSpacing * 1.5) {
        console.log('使用权重分配布局策略')
        
        // 权重分配布局：根据事件重要性分配空间
        let accumulatedPosition = 0
        const rangeValues: number[] = []
        
        timePoints.forEach((date, index) => {
          const weight = timePointWeights.get(date) || 1
          const allocatedWidth = (weight / totalWeight) * availableWidth
          const position = accumulatedPosition + allocatedWidth / 2
          
          rangeValues.push(position)
          accumulatedPosition += allocatedWidth
          
          console.log(`时间点 ${index + 1}: ${date.toLocaleDateString()}, 权重: ${weight.toFixed(2)}, 位置: ${position.toFixed(0)}px`)
        })
        
        // 确保所有位置都在有效范围内
        const normalizedRanges = rangeValues.map(pos => 
          Math.max(minSpacing, Math.min(pos, availableWidth - minSpacing))
        )
        
        xScale = d3.scaleOrdinal<Date, number>()
          .domain(timePoints)
          .range(normalizedRanges)
      } else {
        console.log('使用均匀分段布局策略')
        // 空间充足，使用均匀分段布局
        const segmentWidth = availableWidth / pointCount
        const rangeValues = timePoints.map((_, i) => (i + 0.5) * segmentWidth)
        
        xScale = d3.scaleOrdinal<Date, number>()
          .domain(timePoints)
          .range(rangeValues)
      }
    } else {
      console.log('使用线性时间尺度布局策略')
      // 短时间跨度：使用传统的线性时间尺度，但优化间距
      let padding = timeSpan * 0.05
      
      // 确保最小间距
      const minTotalSpacing = pointCount * 120
      if (availableWidth < minTotalSpacing) {
        padding = timeSpan * 0.02 // 减少padding为紧凑布局让路
      }
      
      const paddedStart = new Date(startDate.getTime() - padding)
      const paddedEnd = new Date(endDate.getTime() + padding)
      
      xScale = d3.scaleTime()
        .domain([paddedStart, paddedEnd])
        .range([0, availableWidth])
    }

    // 绘制事件和时间轴（时间轴需要事件数据来决定显示哪些时间点）
    drawEventsWithImprovedLayout()

    // 设置缩放
    setupZoom(width, height)
  } catch (error) {
    console.error('D3Timeline 初始化错误:', error)
    message.error('时间轴渲染失败')
  }
}

const drawAxis = (eventTimePoints: Set<Date>) => {
  const width = containerRef.value!.clientWidth - MARGIN.left - MARGIN.right
  const centerY = (TIMELINE_HEIGHT - MARGIN.top - MARGIN.bottom) / 2

  // 主时间轴线（居中）
  g.append('line')
    .attr('class', 'timeline-axis')
    .attr('x1', 0)
    .attr('y1', centerY)
    .attr('x2', width)
    .attr('y2', centerY)
    .style('stroke', '#1890ff')
    .style('stroke-width', 3)
    .style('opacity', 0.8)

  // 只显示有事件的时间点
  const timePointsArray = Array.from(eventTimePoints).sort((a, b) => a.getTime() - b.getTime())
  
  // 根据时间跨度选择合适的日期格式
  const getDateFormat = (timePoints: Date[]) => {
    if (timePoints.length <= 1) return d3.timeFormat('%Y-%m-%d')
    
    const span = timePoints[timePoints.length - 1].getTime() - timePoints[0].getTime()
    const days = span / (24 * 60 * 60 * 1000)
    
    if (days > 365) {
      return d3.timeFormat('%Y-%m')
    } else if (days > 30) {
      return d3.timeFormat('%m-%d')
    } else {
      return d3.timeFormat('%m-%d')
    }
  }
  
  const format = getDateFormat(timePointsArray)

  // 时间点标记
  const tickGroups = g.selectAll('.time-tick')
    .data(timePointsArray)
    .enter()
    .append('g')
    .attr('class', 'time-tick')
    .attr('transform', d => {
      // 根据scale类型获取x位置
      const x = typeof xScale === 'function' && 'bandwidth' in xScale 
        ? (xScale as any)(d) // 序数尺度
        : (xScale as d3.ScaleTime<number, number>)(d) // 时间尺度
      return `translate(${x}, ${centerY})`
    })

  // 时间点圆圈
  tickGroups.append('circle')
    .attr('class', 'tick-point')
    .attr('r', 6)
    .style('fill', 'var(--n-card-color)')
    .style('stroke', '#1890ff')
    .style('stroke-width', 2)
    .style('opacity', 0.9)

  // 时间标签（隐藏日期显示）
  // tickGroups.append('text')
  //   .attr('class', 'tick-label')
  //   .attr('x', 0)
  //   .attr('y', 25)
  //   .attr('text-anchor', 'middle')
  //   .style('font-size', '11px')
  //   .style('font-weight', '500')
  //   .style('fill', 'var(--n-text-color-2)')
  //   .text(d => format(d))
}

// 改进版绘制函数，使用基于绝对坐标的碰撞检测
const drawEventsWithImprovedLayout = () => {
  const centerY = (TIMELINE_HEIGHT - MARGIN.top - MARGIN.bottom) / 2

  // 事件节点
  const sortedEvents = [...props.events].sort((a, b) =>
    new Date(a.event_date).getTime() - new Date(b.event_date).getTime()
  )
  
  console.log('🎨 开始绘制事件...')
  console.log('排序后事件数量:', sortedEvents.length)

  // 将事件分组到最近的时间点
  const groupedEvents = groupEventsToTimePoints(sortedEvents)
  
  // 获取有事件的时间点
  const eventTimePoints = new Set(groupedEvents.keys())
  console.log('时间点数量:', eventTimePoints.size)
  
  // 先绘制时间轴（只显示有事件的时间点）
  drawAxis(eventTimePoints)
  
  // 使用改进的布局算法，传入时间尺度进行精确碰撞检测
  const eventLayouts = calculateEventLayoutsWithScale(groupedEvents, xScale)
  
  console.log('布局计算结果:', eventLayouts)

  // 为每个时间点创建事件组
  eventLayouts.forEach((layouts, timePoint) => {
    const eventsCountSameDay = layouts.length // 获取同一天的事件数量
    console.log(`📅 绘制时间点 ${timePoint.toLocaleDateString()}: ${eventsCountSameDay} 个事件`)

    layouts.forEach((layout, layoutIndex) => {
      console.log(`  🎯 绘制事件 ${layoutIndex + 1}: ${layout.event.title}`)
      const { event, side, yOffset, xOffset, availableWidth, absoluteX } = layout
      const config = getResponsiveConfig()

      // 使用预计算的绝对X坐标
      const finalX = absoluteX

      // 创建事件组
      const eventGroup = g.append('g')
        .attr('class', 'event-group')
        .attr('transform', `translate(${finalX}, ${centerY})`)

      // 创建连接线（从时间轴中心点连接到偏移后的卡片）
      const offsetX = xOffset || 0
      let pathData: string

      if (Math.abs(offsetX) < 5) {
        // 没有水平偏移时，使用直接的曲线
        pathData = `M ${-offsetX},0 L ${-offsetX},${side * 20} Q ${-offsetX},${side * 35} ${-offsetX},${yOffset}`
      } else {
        // 有水平偏移时，创建更自然的连接曲线
        const midY = side * 30
        pathData = `M ${-offsetX},0 Q ${-offsetX * 0.5},${midY * 0.3} 0,${midY} Q 0,${side * 35} 0,${yOffset}`
      }

      eventGroup.append('path')
        .attr('class', 'connection-line')
        .attr('d', pathData)
        .style('fill', 'none')
        .style('stroke', getEventTypeColor(event))
        .style('stroke-width', 2)
        .style('opacity', 0.6)

      // 卡片组容器
      const cardGroup = eventGroup.append('g')
        .attr('class', 'event-card')
        .attr('transform', `translate(0, ${yOffset})`)
        .style('cursor', props.editMode ? 'grab' : 'pointer')
        .on('mouseover', function() {
          const card = d3.select(this)

          // 高亮连接线
          eventGroup.select('.connection-line')
            .transition()
            .duration(200)
            .style('opacity', 1)
            .style('stroke-width', 3)

          card.select('.card-bg')
            .transition()
            .duration(200)
            .style('fill', props.editMode ? 'var(--n-warning-color-hover, #fff7e6)' : 'var(--n-card-color-hover, #fafbfc)')
            .style('stroke', props.editMode ? 'var(--n-warning-color)' : getEventTypeColor(event))
            .style('stroke-width', 2)
        })
        .on('mouseout', function() {
          const card = d3.select(this)

          // 恢复连接线
          eventGroup.select('.connection-line')
            .transition()
            .duration(200)
            .style('opacity', 0.6)
            .style('stroke-width', 2)

          card.select('.card-bg')
            .transition()
            .duration(200)
            .style('fill', 'var(--n-card-color, #ffffff)')
            .style('stroke', 'var(--n-border-color)')
            .style('stroke-width', 1)
        })
        .on('click', () => {
          if (props.editMode) {
            // 编辑模式下直接编辑事件
            emit('edit', event)
          } else {
            // 非编辑模式下显示详情弹窗
            selectedEvent.value = event
            showDetailModal.value = true
          }
        })

      // 绘制卡片内容，传递同一天事件数量
      drawEventCard(cardGroup, event, config, availableWidth, eventsCountSameDay)
    })
  })
}

// 通用卡片绘制函数
const drawEventCard = (
  cardGroup: d3.Selection<SVGGElement, unknown, null, undefined>,
  event: EconomicsHistoryEvent,
  config: any,
  availableWidth: number,
  eventsCountSameDay: number = 1
) => {
  // 根据同一天事件数量动态调整卡片尺寸
  let cardWidth = Math.min(config.cardWidth, availableWidth)
  let cardHeight = config.cardHeight

  // 当同一天事件较多时，适度缩小卡片
  if (eventsCountSameDay > 3) {
    const scaleFactor = Math.max(0.75, 1 - (eventsCountSameDay - 3) * 0.05)
    cardWidth = Math.max(cardWidth * scaleFactor, 120)
    cardHeight = Math.max(cardHeight * scaleFactor, 45)
  }
  
  // 卡片背景
  cardGroup.append('rect')
    .attr('class', 'card-bg')
    .attr('x', -cardWidth / 2)
    .attr('y', -cardHeight / 2)
    .attr('width', cardWidth)
    .attr('height', cardHeight)
    .attr('rx', 8)
    .style('fill', 'var(--n-card-color, #ffffff)')
    .style('stroke', 'var(--n-border-color)')
    .style('stroke-width', 1)
    .style('filter', 'drop-shadow(0 2px 8px rgba(0,0,0,0.08))')

  // 重要程度装饰条
  cardGroup.append('rect')
    .attr('class', 'importance-bar')
    .attr('x', -cardWidth / 2)
    .attr('y', -cardHeight / 2)
    .attr('width', cardWidth)
    .attr('height', 4)
    .attr('rx', 8)
    .style('fill', getEventTypeColor(event))

  // 根据同一天事件数量动态调整字体大小和内容显示
  const fontSizeMultiplier = eventsCountSameDay > 6 ? 0.8 : eventsCountSameDay > 4 ? 0.9 : 1
  const titleFontSize = Math.max(12 * fontSizeMultiplier, 10)
  const typeFontSize = Math.max(10 * fontSizeMultiplier, 8)
  const dateFontSize = Math.max(10 * fontSizeMultiplier, 8)

  // 计算文字区域，完全避开右上角30px区域
  const textAreaWidth = cardWidth - 50 // 为右侧星星预留更多空间
  const charWidth = titleFontSize * 0.6 // 根据字体大小调整字符宽度估算
  const maxTitleChars = Math.floor(textAreaWidth / charWidth)
  const displayTitle = event.title.length > maxTitleChars ? event.title.slice(0, maxTitleChars - 3) + '...' : event.title

  // 卡片标题（限制在左侧区域）
  cardGroup.append('text')
    .attr('class', 'card-title')
    .attr('x', -cardWidth/2 + textAreaWidth/2)
    .attr('y', -5)
    .attr('text-anchor', 'middle')
    .style('font-size', `${titleFontSize}px`)
    .style('font-weight', '600')
    .style('fill', 'var(--n-text-color-1)')
    .style('user-select', 'none')
    .text(displayTitle)

  // 事件类型（左下方）
  cardGroup.append('text')
    .attr('class', 'card-type')
    .attr('x', -cardWidth/2 + 8)
    .attr('y', cardHeight/2 - 20)
    .attr('text-anchor', 'start')
    .style('font-size', `${typeFontSize}px`)
    .style('font-weight', '500')
    .style('fill', getEventTypeColor(event))
    .style('user-select', 'none')
    .text(getEventTypeDisplayName(event))

  // 事件日期（右下方，但避开星星）
  cardGroup.append('text')
    .attr('class', 'card-date')
    .attr('x', cardWidth/2 - 8)
    .attr('y', cardHeight/2 - 8)
    .attr('text-anchor', 'end')
    .style('font-size', `${dateFontSize}px`)
    .style('fill', 'var(--n-text-color-3)')
    .style('user-select', 'none')
    .text(formatDate(event.event_date))

  // 重要程度星标（紧贴右上角）
  if (event.importance_level > 0) {
    // 创建星标背景圆形（更小，紧贴角落）
    cardGroup.append('circle')
      .attr('class', 'star-bg')
      .attr('cx', cardWidth/2 - 12)
      .attr('cy', -cardHeight/2 + 12)
      .attr('r', 10)
      .style('fill', 'rgba(255, 193, 7, 0.15)')
      .style('stroke', '#ffc107')
      .style('stroke-width', 1)
      .style('opacity', 0.9)

    // 星标数字（更紧凑）
    cardGroup.append('text')
      .attr('class', 'star-number')
      .attr('x', cardWidth/2 - 12)
      .attr('y', -cardHeight/2 + 16)
      .attr('text-anchor', 'middle')
      .style('font-size', '10px')
      .style('font-weight', '700')
      .style('fill', '#f57c00')
      .style('user-select', 'none')
      .text(event.importance_level.toString())

    // 小星形装饰（位置更靠角落）
    const starSize = 1.5
    const starPath = `M0,-${starSize}L${starSize*0.3},-${starSize*0.3}L${starSize},0L${starSize*0.3},${starSize*0.3}L0,${starSize}L-${starSize*0.3},${starSize*0.3}L-${starSize},0L-${starSize*0.3},-${starSize*0.3}Z`
    
    cardGroup.append('path')
      .attr('class', 'star-icon')
      .attr('d', starPath)
      .attr('transform', `translate(${cardWidth/2 - 20}, ${-cardHeight/2 + 6})`)
      .style('fill', '#ffc107')
      .style('opacity', 0.8)
  }

  // 编辑模式下的删除按钮
  if (props.editMode) {
    // 删除按钮背景
    const deleteBtn = cardGroup.append('g')
      .attr('class', 'delete-btn')
      .attr('transform', `translate(${cardWidth/2 - 8}, ${-cardHeight/2 + 8})`)
      .style('cursor', 'pointer')
      .style('opacity', 0.7)
      .on('mouseover', function() {
        d3.select(this).style('opacity', 1)
      })
      .on('mouseout', function() {
        d3.select(this).style('opacity', 0.7)
      })
      .on('click', function(e) {
        e.stopPropagation() // 阻止卡片点击事件
        emit('delete', event)
      })

    deleteBtn.append('circle')
      .attr('r', 8)
      .style('fill', '#ff4d4f')
      .style('stroke', '#ffffff')
      .style('stroke-width', 1)

    // 删除图标（X）
    deleteBtn.append('path')
      .attr('d', 'M-3,-3 L3,3 M3,-3 L-3,3')
      .style('stroke', '#ffffff')
      .style('stroke-width', 1.5)
      .style('stroke-linecap', 'round')
  }
}

// 保留原始绘制函数作为备用
const drawEvents = () => {
  const centerY = (TIMELINE_HEIGHT - MARGIN.top - MARGIN.bottom) / 2

  // 事件节点
  const sortedEvents = [...props.events].sort((a, b) =>
    new Date(a.event_date).getTime() - new Date(b.event_date).getTime()
  )

  // 将事件分组到最近的时间点
  const groupedEvents = groupEventsToTimePoints(sortedEvents)
  
  // 获取有事件的时间点
  const eventTimePoints = new Set(groupedEvents.keys())
  
  // 先绘制时间轴（只显示有事件的时间点）
  drawAxis(eventTimePoints)
  
  // 计算每个时间点的事件布局
  const eventLayouts = calculateEventLayouts(groupedEvents)

  // 为每个时间点创建事件组
  eventLayouts.forEach((layouts, timePoint) => {
    // 根据scale类型获取x位置
    let timePointX: number
    
    if (typeof xScale === 'function' && 'bandwidth' in xScale) {
      // 序数尺度
      timePointX = (xScale as any)(timePoint)
      // 如果序数尺度没有找到对应的值，需要处理
      if (timePointX === undefined) {
        console.warn('序数尺度未找到时间点:', timePoint)
        // 尝试找到最近的时间点
        const domain = (xScale as any).domain() as Date[]
        const closestPoint = domain.reduce((prev, curr) => 
          Math.abs(curr.getTime() - timePoint.getTime()) < Math.abs(prev.getTime() - timePoint.getTime()) ? curr : prev
        )
        timePointX = (xScale as any)(closestPoint) || 0
      }
    } else {
      // 时间尺度
      timePointX = (xScale as d3.ScaleTime<number, number>)(timePoint)
    }
    
    // 确保坐标有效
    if (isNaN(timePointX) || timePointX === undefined) {
      console.warn('无效的时间点坐标:', timePoint, timePointX)
      return // 跳过这个时间点的渲染
    }
    
    layouts.forEach((layout) => {
      const { event, side, yOffset, xOffset, availableWidth } = layout
      const config = getResponsiveConfig()
      
      // 计算最终的x位置（基础位置 + 水平偏移）
      const finalX = timePointX + (xOffset || 0)
      
      // 创建事件组（以中心时间轴为基准，加上水平偏移）
      const eventGroup = g.append('g')
        .attr('class', 'event-group')
        .attr('transform', `translate(${finalX}, ${centerY})`)

      // 创建连接线（从时间轴中心点连接到偏移后的卡片）
      // 计算连接线路径：考虑水平偏移
      const offsetX = xOffset || 0
      let pathData: string
      
      if (Math.abs(offsetX) < 5) {
        // 没有水平偏移时，使用直接的曲线
        pathData = `M ${-offsetX},0 L ${-offsetX},${side * 20} Q ${-offsetX},${side * 35} ${-offsetX},${yOffset}`
      } else {
        // 有水平偏移时，创建更自然的连接曲线
        const midY = side * 30
        pathData = `M ${-offsetX},0 Q ${-offsetX * 0.5},${midY * 0.3} 0,${midY} Q 0,${side * 35} 0,${yOffset}`
      }
      
      eventGroup.append('path')
        .attr('class', 'connection-line')
        .attr('d', pathData)
        .style('fill', 'none')
        .style('stroke', getEventTypeColor(event))
        .style('stroke-width', 2)
        .style('opacity', 0.6)

      // 卡片组容器
      const cardGroup = eventGroup.append('g')
        .attr('class', 'event-card')
        .attr('transform', `translate(0, ${yOffset})`)
        .style('cursor', props.editMode ? 'grab' : 'pointer')
        .on('mouseover', function() {
          const card = d3.select(this)
          
          // 高亮连接线
          eventGroup.select('.connection-line')
            .transition()
            .duration(200)
            .style('opacity', 1)
            .style('stroke-width', 3)
          
          card.select('.card-bg')
            .transition()
            .duration(200)
            .style('fill', props.editMode ? 'var(--n-warning-color-hover, #fff7e6)' : 'var(--n-card-color-hover, #fafbfc)')
            .style('stroke', props.editMode ? 'var(--n-warning-color)' : getEventTypeColor(event))
            .style('stroke-width', 2)
        })
        .on('mouseout', function() {
          const card = d3.select(this)
          
          // 恢复连接线
          eventGroup.select('.connection-line')
            .transition()
            .duration(200)
            .style('opacity', 0.6)
            .style('stroke-width', 2)
          
          card.select('.card-bg')
            .transition()
            .duration(200)
            .style('fill', 'var(--n-card-color, #ffffff)')
            .style('stroke', 'var(--n-border-color)')
            .style('stroke-width', 1)
        })
        .on('click', () => {
          if (props.editMode) {
            // 编辑模式下直接编辑事件
            emit('edit', event)
          } else {
            // 非编辑模式下显示详情弹窗
            selectedEvent.value = event
            showDetailModal.value = true
          }
        })

      // 卡片尺寸
      const cardWidth = Math.min(config.cardWidth, availableWidth)
      const cardHeight = config.cardHeight
      
      // 卡片背景
      cardGroup.append('rect')
        .attr('class', 'card-bg')
        .attr('x', -cardWidth / 2)
        .attr('y', -cardHeight / 2)
        .attr('width', cardWidth)
        .attr('height', cardHeight)
        .attr('rx', 8)
        .style('fill', 'var(--n-card-color, #ffffff)')
        .style('stroke', 'var(--n-border-color)')
        .style('stroke-width', 1)
        .style('filter', 'drop-shadow(0 2px 8px rgba(0,0,0,0.08))')

      // 重要程度装饰条
      cardGroup.append('rect')
        .attr('class', 'importance-bar')
        .attr('x', -cardWidth / 2)
        .attr('y', -cardHeight / 2)
        .attr('width', cardWidth)
        .attr('height', 4)
        .attr('rx', 8)
        .style('fill', getEventTypeColor(event))

      // 计算文字区域，完全避开右上角30px区域
      const textAreaWidth = cardWidth - 50 // 为右侧星星预留更多空间
      const maxTitleChars = Math.floor(textAreaWidth / 12)
      const displayTitle = event.title.length > maxTitleChars ? event.title.slice(0, maxTitleChars - 3) + '...' : event.title

      // 卡片标题（限制在左侧区域）
      cardGroup.append('text')
        .attr('class', 'card-title')
        .attr('x', -cardWidth/2 + textAreaWidth/2)
        .attr('y', -5)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('font-weight', '600')
        .style('fill', 'var(--n-text-color-1)')
        .style('user-select', 'none')
        .text(displayTitle)

      // 事件类型（左下方）
      cardGroup.append('text')
        .attr('class', 'card-type')
        .attr('x', -cardWidth/2 + 8)
        .attr('y', cardHeight/2 - 20)
        .attr('text-anchor', 'start')
        .style('font-size', '10px')
        .style('font-weight', '500')
        .style('fill', getEventTypeColor(event))
        .style('user-select', 'none')
        .text(getEventTypeDisplayName(event))

      // 事件日期（右下方，但避开星星）
      cardGroup.append('text')
        .attr('class', 'card-date')
        .attr('x', cardWidth/2 - 8)
        .attr('y', cardHeight/2 - 8)
        .attr('text-anchor', 'end')
        .style('font-size', '10px')
        .style('fill', 'var(--n-text-color-3)')
        .style('user-select', 'none')
        .text(formatDate(event.event_date))

      // 重要程度星标（紧贴右上角）
      if (event.importance_level > 0) {
        // 创建星标背景圆形（更小，紧贴角落）
        cardGroup.append('circle')
          .attr('class', 'star-bg')
          .attr('cx', cardWidth/2 - 12)
          .attr('cy', -cardHeight/2 + 12)
          .attr('r', 10)
          .style('fill', 'rgba(255, 193, 7, 0.15)')
          .style('stroke', '#ffc107')
          .style('stroke-width', 1)
          .style('opacity', 0.9)

        // 星标数字（更紧凑）
        cardGroup.append('text')
          .attr('class', 'star-number')
          .attr('x', cardWidth/2 - 12)
          .attr('y', -cardHeight/2 + 16)
          .attr('text-anchor', 'middle')
          .style('font-size', '10px')
          .style('font-weight', '700')
          .style('fill', '#f57c00')
          .style('user-select', 'none')
          .text(event.importance_level.toString())

        // 小星形装饰（位置更靠角落）
        const starSize = 1.5
        const starPath = `M0,-${starSize}L${starSize*0.3},-${starSize*0.3}L${starSize},0L${starSize*0.3},${starSize*0.3}L0,${starSize}L-${starSize*0.3},${starSize*0.3}L-${starSize},0L-${starSize*0.3},-${starSize*0.3}Z`
        
        cardGroup.append('path')
          .attr('class', 'star-icon')
          .attr('d', starPath)
          .attr('transform', `translate(${cardWidth/2 - 20}, ${-cardHeight/2 + 6})`)
          .style('fill', '#ffc107')
          .style('opacity', 0.8)
      }

      // 编辑模式下的删除按钮
      if (props.editMode) {
        // 删除按钮背景
        const deleteBtn = cardGroup.append('g')
          .attr('class', 'delete-btn')
          .attr('transform', `translate(${cardWidth/2 - 8}, ${-cardHeight/2 + 8})`)
          .style('cursor', 'pointer')
          .style('opacity', 0.7)
          .on('mouseover', function() {
            d3.select(this).style('opacity', 1)
          })
          .on('mouseout', function() {
            d3.select(this).style('opacity', 0.7)
          })
          .on('click', function(e) {
            e.stopPropagation() // 阻止卡片点击事件
            emit('delete', event)
          })

        deleteBtn.append('circle')
          .attr('r', 8)
          .style('fill', '#ff4d4f')
          .style('stroke', '#ffffff')
          .style('stroke-width', 1)

        // 删除图标（X）
        deleteBtn.append('path')
          .attr('d', 'M-3,-3 L3,3 M3,-3 L-3,3')
          .style('stroke', '#ffffff')
          .style('stroke-width', 1.5)
          .style('stroke-linecap', 'round')
      }
    })
  })
}

// 提示框
let tooltip: d3.Selection<HTMLDivElement, unknown, HTMLElement, any>

const createTooltip = () => {
  tooltip = d3.select('body')
    .append('div')
    .attr('class', 'd3-tooltip')
    .style('opacity', 0)
    .style('position', 'absolute')
    .style('background', 'var(--n-card-color, #ffffff)')
    .style('color', 'var(--n-text-color-1)')
    .style('padding', '12px 16px')
    .style('border-radius', '6px')
    .style('font-size', '13px')
    .style('pointer-events', 'none')
    .style('z-index', '1000')
    .style('box-shadow', '0 4px 12px rgba(0, 0, 0, 0.1)')
    .style('border', '1px solid var(--n-border-color)')
    .style('max-width', '280px')
}

const showTooltip = (event: any, d: EconomicsHistoryEvent) => {
  if (!tooltip) createTooltip()

  tooltip.transition()
    .duration(150)
    .style('opacity', 1)

  const stars = '★'.repeat(d.importance_level) + '☆'.repeat(5 - d.importance_level)

  tooltip.html(`
    <div class="tooltip-title">${d.title}</div>
    <div class="tooltip-row">
      <span class="tooltip-label">类型:</span>
      <span class="tooltip-value">${getEventTypeDisplayName(d)}</span>
    </div>
    <div class="tooltip-row">
      <span class="tooltip-label">日期:</span>
      <span class="tooltip-value">${formatDate(d.event_date)}</span>
    </div>
    <div class="tooltip-row">
      <span class="tooltip-label">重要:</span>
      <span class="tooltip-stars">${stars}</span>
    </div>
    ${d.description ? `<div class="tooltip-desc">${d.description.slice(0, 60)}...</div>` : ''}
  `)
  .style('left', (event.pageX + 15) + 'px')
  .style('top', (event.pageY - 15) + 'px')
}

const hideTooltip = () => {
  if (tooltip) {
    tooltip.transition()
      .duration(150)
      .style('opacity', 0)
  }
}

// 缩放功能
const setupZoom = (width: number, height: number) => {
  zoom = d3.zoom<SVGElement, unknown>()
    .scaleExtent([0.5, 5])
    .translateExtent([[-MARGIN.left, -MARGIN.top], [width + MARGIN.right, height + MARGIN.bottom]])
    .on('zoom', (event) => {
      const { transform } = event
      g.attr('transform', `translate(${MARGIN.left + transform.x},${MARGIN.top + transform.y}) scale(${transform.k})`)

      zoomLevel.value = transform.k
      canZoomIn.value = transform.k < 5
      canZoomOut.value = transform.k > 0.5
    })

  svg.call(zoom)
}

const zoomIn = () => {
  svg.transition().duration(300).call(
    zoom.scaleBy, 1.5
  )
}

const zoomOut = () => {
  svg.transition().duration(300).call(
    zoom.scaleBy, 1 / 1.5
  )
}

const resetZoom = () => {
  svg.transition().duration(500).call(
    zoom.transform,
    d3.zoomIdentity
  )
}

// 响应式处理
let resizeObserver: ResizeObserver | null = null
let resizeTimeout: number | null = null

const handleResize = () => {
  // 防抖：避免频繁重绘
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
  
  resizeTimeout = window.setTimeout(() => {
    nextTick(() => {
      initD3Timeline()
    })
  }, 150) // 150ms 防抖延迟
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initD3Timeline()

    // 监听容器大小变化
    if (containerRef.value) {
      resizeObserver = new ResizeObserver(handleResize)
      resizeObserver.observe(containerRef.value)
    }
  })
})

onUnmounted(() => {
  // 清理提示框
  if (tooltip) {
    tooltip.remove()
  }

  // 清理观察器
  if (resizeObserver && containerRef.value) {
    resizeObserver.unobserve(containerRef.value)
  }
  
  // 清理防抖定时器
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }
})

// 监听数据变化
watch(() => props.events, () => {
  nextTick(() => {
    initD3Timeline()
  })
}, { deep: true })
</script>

<style scoped>
.d3-timeline {
  width: 100%;
  background: var(--n-card-color);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--n-border-color);
}

.timeline-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--n-border-color);
  transition: all 0.3s ease;
}

.timeline-controls.edit-mode {
  background: var(--n-warning-color-supressed, #fff7e6);
  border-left: 4px solid var(--n-warning-color);
}

.timeline-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.svg-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.timeline-svg {
  width: 100%;
  height: 800px;
  cursor: grab;
  background: var(--n-card-color);
}

.timeline-svg:active {
  cursor: grabbing;
}

/* 事件详情样式 */
.event-detail {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.detail-date {
  font-size: 14px;
  color: var(--n-text-color-3);
}

.detail-description p {
  margin: 0;
  line-height: 1.6;
  color: var(--n-text-color-2);
}

.detail-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 弹窗样式 */
.event-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-header {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.detail-date {
  font-size: 14px;
  color: var(--n-text-color-2);
  font-weight: 500;
}

.detail-importance {
  display: flex;
  align-items: center;
  gap: 8px;
}

.importance-label {
  font-size: 13px;
  color: var(--n-text-color-3);
}

.detail-description h4,
.detail-tags h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.detail-description p {
  margin: 0;
  line-height: 1.6;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.tags-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式 */
@media (max-width: 768px) {
  .timeline-controls {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .timeline-svg {
    height: 800px;
  }

  .timeline-title {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .timeline-svg {
    height: 800px;
  }
  
  .timeline-controls {
    padding: 10px 12px;
  }
  
  .timeline-title {
    font-size: 14px;
  }
}

@media (max-width: 360px) {
  .timeline-svg {
    height: 800px;
  }
}
</style>

<style>
/* 全局D3提示框样式 */
.d3-tooltip {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
}

.d3-tooltip .tooltip-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--n-text-color-1);
  border-bottom: 1px solid var(--n-border-color);
  padding-bottom: 6px;
}

.d3-tooltip .tooltip-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.d3-tooltip .tooltip-label {
  opacity: 0.7;
  margin-right: 12px;
  color: var(--n-text-color-3);
}

.d3-tooltip .tooltip-value {
  font-weight: 500;
  color: var(--n-text-color-1);
}

.d3-tooltip .tooltip-stars {
  color: #ffd700;
  font-size: 10px;
  letter-spacing: 1px;
}

.d3-tooltip .tooltip-desc {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--n-border-color);
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.4;
  color: var(--n-text-color-2);
}

/* 移动端响应式优化 - 与上方样式合并 */
@media (max-width: 768px) {
  .d3-tooltip {
    max-width: 280px;
    font-size: 12px;
    padding: 10px 14px;
  }
  
  .controls-right .n-space {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .d3-tooltip {
    max-width: 240px;
    font-size: 11px;
    padding: 8px 12px;
  }
  
  .controls-left,
  .controls-right {
    width: 100%;
  }
  
  .controls-right {
    display: flex;
    justify-content: flex-end;
  }
}

@media (max-width: 360px) {
  .d3-tooltip {
    max-width: 200px;
    font-size: 10px;
    padding: 6px 10px;
  }
}
</style>